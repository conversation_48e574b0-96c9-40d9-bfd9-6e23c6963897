import { loadEnv, type UserConfig } from 'vite'
import { resolve } from 'path'
import ViteBasePlugins from './Plugins/index'

export default function ({ mode, baseUrl }): UserConfig {
  const cwd = process.cwd()
  const env = loadEnv(mode, resolve(__dirname, './Env'), '')

  const config:any = {
    plugins: [],
    resolve: {
      alias: {
        '@': resolve(cwd, 'src'),
        '#': resolve(cwd, '../../')
      }
    },
    base: baseUrl,
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "src/scss/index.scss";`
        }
      }
    },
    server: {
      cors: true, // 允许跨域
      host: true
    },
    build: {
      chunkSizeWarningLimit: 4000, // 消除打包大小超过500kb警告
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
          // 最小化拆分包
          // manualChunks(id) {
          //   if (id.includes('node_modules')) {
          //     return id.toString().split('node_modules/')[1].split('/')[0].toString()
          //   }
          // }
        }
      }
    },
    envDir: resolve(__dirname, './Env'),
  }

  if (env.VITE_ENV === 'master') {
    config.esbuild = {
      pure: ['console.log'], // 删除 console.log
      drop: ['debugger'], // 删除 debugger
    }
  }

  ViteBasePlugins(config, { env: env.VITE_ENV, baseUrl })

  return config
}
