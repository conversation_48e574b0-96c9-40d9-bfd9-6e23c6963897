/*
 * @Date         : 2024-01-22 15:18:36
 * @Description  : 打包静态文件自动上传oss
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { resolve } from 'path'
import OnionOssVitePlugin from '@guanghe-pub/onion-oss-vite-plugin'

export default function (plugins, config, { env, baseUrl }) {
  if (env === 'master' || env === 'stage') { // cnd配置 预发/生产环境开启
    plugins.push(
      OnionOssVitePlugin({
        output: resolve(process.cwd(), 'dist'),
        rootDir: baseUrl
      })
    )

    config.base = `//fp.yangcong345.com${baseUrl}`
  }
}
