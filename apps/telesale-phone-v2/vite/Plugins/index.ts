import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver, ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import viteCompression from 'vite-plugin-compression'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

import UnoCss from 'unocss/vite'
import legacy from '@vitejs/plugin-legacy'

import HtmlPlugin from './modules/HtmlPlugin'
import Visualizer from './modules/VisualizerPlugin'
import OnionOss from './modules/OnionOssPlugin'
import vueSetupExtend from 'vite-plugin-vue-setup-extend'

export default function (config, { env, baseUrl }) {
  const plugins = [
    vue(),
    vueSetupExtend(), // 配置在setup script标签上添加name 以在keepalive中使用include
    legacy({
      targets: ['> 1%', 'IOS >= 11', 'Android >= 5']
    }),
    createSvgIconsPlugin({
      // 指定需要缓存的图标文件夹
      iconDirs: [resolve(process.cwd(), 'src/assets/svg')],
      // 指定symbolId格式
      symbolId: 'icon-[name]'
    }),
    AutoImport({
      resolvers: [VantResolver(), ElementPlusResolver()],
      imports: ['vue', 'vue-router', 'pinia']
    }),
    Components({
      resolvers: [VantResolver(), ElementPlusResolver()]
    }),
    viteCompression({
      verbose: true, // 是否在控制台中输出压缩结果
      disable: false,
      threshold: 10240, // 如果体积大于阈值，将被压缩，单位为b，体积过小时请不要压缩，以免适得其反
      algorithm: 'gzip', // 压缩算法，可选['gzip'，' brotliccompress '，'deflate '，'deflateRaw']
      ext: '.gz',
    }),
    UnoCss()
  ]

  HtmlPlugin(plugins, env)
  OnionOss(plugins, config, { env, baseUrl })
  Visualizer(plugins, env)

  config.plugins.push(plugins)

  return plugins
}
