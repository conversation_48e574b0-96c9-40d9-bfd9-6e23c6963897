/*
 * @Date         : 2024-07-17 16:05:55
 * @Description  : 开始通话计时
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */
import { useCall } from '@/store/call'

export default function() {
  const callStore = useCall()

  let countInterval

  function startCount() {
    countInterval = setInterval(() => {
      callStore.callTime++
    }, 1000)
  }

  function endCount() {
    clearInterval(countInterval)
    callStore.callTime = 0
  }

  return {
    startCount,
    endCount,
  }
}
