<!--
 * @Date         : 2024-07-17 10:49:26
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="header relative">
    <div class=" flex items-center justify-between h-46PX bg-white px-16PX">
      <div class="flex">
        <van-popover v-model:show="showInfo"
                     placement="bottom-start">
          <template #reference>
            <div class="flexD"
                 @mouseleave="showInfo=false"
                 @mouseenter="showInfo=true">
              <el-icon v-if="callStore.state"
                       size="20px"
                       color="#67C23A"><CircleCheck /></el-icon>
              <el-icon v-else
                       size="20px"
                       color="#F56C6C"><CircleClose /></el-icon>
              <el-text class="ml-5px!"
                       :type="callStore.state ? 'success' : 'danger'">{{ callStore.state ? '在线' : '离线' }}</el-text>
            </div>

          </template>

          <div class="px-20px pt-20px"
               @mouseenter="showInfo=true"
               @mouseleave="showInfo=false">
            <el-descriptions :column="1">
              <el-descriptions-item label="外呼渠道">{{ userStore.channel }}</el-descriptions-item>
              <el-descriptions-item label="外呼工号">{{ userStore.qmExten }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </van-popover>

        <div class="ml-20px">
          <common-icon name="ding"
                       :color="isDing ? '#409EFF': '#909399'"
                       size="18px"
                       @click="changeDing" />
        </div>
      </div>

      <van-popover v-model:show="showPopover"
                   placement="bottom-end">
        <template #default>
          <div class="flexD flex-col">
            <van-cell v-if="configStore.configSupport"
                      center
                      title="本地缓存">
              <template #right-icon>
                <van-switch v-model="configStore.cacheChecked"
                            :disabled="callStore.isCalling"
                            size="18px"
                            @change="configStore.changeConfig(false)" />
              </template>
            </van-cell>

            <van-cell v-if="configStore.configSupport"
                      center
                      title="挂断提示音">
              <template #right-icon>
                <van-switch v-model="configStore.hangupChecked"
                            :disabled="callStore.isCalling"
                            size="18px"
                            @change="configStore.changeConfig(false)" />
              </template>
            </van-cell>

            <van-cell center
                      title="退出登录"
                      @click="logout" />
          </div>
        </template>

        <template #reference>
          <div class="flexD">
            <el-avatar class="mr-10px"
                       :src="userStore.avatar"
                       size="small" />
            <el-text>{{ sliceName }}</el-text>
          </div>
        </template>
      </van-popover>
    </div>
  </div>
</template>

<script lang='ts' setup>
import { CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { useUser } from '@/store/user'
import { useCall } from '@/store/call'
import useDing from '../hooks/useDing'
import { useConfig } from '@/store/config'

const configStore = useConfig()
const callStore = useCall()
const userStore = useUser()
const { isDing, changeDing } = useDing()

const showPopover = ref(false)
const showInfo = ref(false)

async function logout() {
  await ElMessageBox.confirm('请确认是否退出登录？', '提示', {
    confirmButtonText: '退出',
    cancelButtonText: '取消'
  })
  userStore.logout()
}

const sliceName = computed(() => {
  const maxLenght = 5
  return userStore.name.length > maxLenght ? userStore.name.slice(0, maxLenght) + '...' : userStore.name
})
</script>

<style scoped lang='scss'>
.header {
  &::after {
    border-bottom-width: var(--van-border-width) !important;
    position: absolute;
    box-sizing: border-box;
    content: " ";
    pointer-events: none;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid var(--van-border-color);
    transform: scale(.5);
  }
}
</style>
