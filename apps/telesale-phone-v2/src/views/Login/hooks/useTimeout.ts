interface TimeoutOptions {
  destroyed: boolean
}

export const useTimeout = (options: TimeoutOptions = { destroyed: true }) => {
  const timeoutId = ref<any>(null)
  const count = ref<number>(0)
  let startTime = 0
  const start = (callback: () => void, delay: number) => {
    if (startTime === 0) startTime = Number(new Date()) / 1000
    timeoutId.value = setTimeout(() => {
      const current = Number(new Date()) / 1000
      count.value = parseInt(String(current - startTime))
      callback()
    }, delay)
  }

  const stop = () => {
    if (timeoutId.value) {
      startTime = 0
      clearInterval(timeoutId.value)
    }
  }

  onUnmounted(() => {
    if (options.destroyed) {
      stop()
    }
  })

  return {
    start,
    stop,
    count
  }
}
