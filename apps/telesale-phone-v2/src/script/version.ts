import { invoke } from '@tauri-apps/api/core'
import { getCurrentWindow } from '@tauri-apps/api/window'

export function getVersion(): Promise<string> {
  return new Promise((resolve, reject) => {
    const winHandler = getCurrentWindow()
    winHandler.title().then((title: string) => {
      const version = title.replace('洋葱学园(', '').replace(')', '')
      console.log('app version is ', version)
      resolve(version)
    })
  })
}
