# Tauri v2 迁移指南

本文档详细说明了从 Tauri v1.6.0 升级到 v2.6.0 的所有破坏性更新和迁移步骤。

## 1. 核心 API 导入变更

### 1.1 invoke 函数
**v1.x:**
```typescript
import { invoke } from '@tauri-apps/api'
```

**v2.x:**
```typescript
import { invoke } from '@tauri-apps/api/core'
```

### 1.2 事件监听
**v1.x:**
```typescript
import { event } from '@tauri-apps/api'
const unlisten = await event.listen('event-name', handler)
```

**v2.x:**
```typescript
import { listen } from '@tauri-apps/api/event'
const unlisten = await listen('event-name', handler)
```

### 1.3 窗口操作
**v1.x:**
```typescript
import { window as win } from '@tauri-apps/api'
const winHandler = win.getCurrent()
await win.appWindow.setAlwaysOnTop(true)
```

**v2.x:**
```typescript
import { getCurrentWindow } from '@tauri-apps/api/window'
const winHandler = getCurrentWindow()
await winHandler.setAlwaysOnTop(true)
```

## 2. 主要破坏性更新

### 2.1 模块重组
- `@tauri-apps/api/tauri` → `@tauri-apps/api/core`
- 窗口相关 API 移动到 `@tauri-apps/api/window`
- 事件相关 API 移动到 `@tauri-apps/api/event`

### 2.2 Window API 变更
- `Window` 类重命名为 `WebviewWindow`
- `window.appWindow` 不再可用，使用 `getCurrentWindow()` 替代
- 窗口事件监听方式保持不变，但导入路径变更

### 2.3 事件系统变更
- `event.listen()` → `listen()`
- `event.emit()` → `emit()`
- 事件处理器签名保持不变

## 3. 项目中的具体变更

### 3.1 文件变更清单

| 文件路径 | 变更类型 | 说明 |
|---------|---------|------|
| `src/script/version.ts` | 导入变更 | invoke, getCurrentWindow |
| `src/store/config.ts` | 导入变更 | invoke |
| `src/views/Home/script/connect.ts` | 导入变更 | invoke |
| `src/views/Home/hooks/useCall.ts` | 导入变更 | invoke, listen |
| `src/views/Home/hooks/useMute.ts` | 导入变更 | invoke |
| `src/views/Home/hooks/useDing.ts` | 导入+API变更 | getCurrentWindow, API调用方式 |

### 3.2 配置文件变更

| 文件 | 变更内容 |
|------|---------|
| `package.json` | 项目名称、依赖版本 |
| `vite.config.ts` | baseUrl 路径 |
| `src/router/index.ts` | 路由 base 路径 |

## 4. 兼容性检查清单

- [ ] 所有 `@tauri-apps/api` 导入已更新
- [ ] 窗口操作 API 已迁移到新的调用方式
- [ ] 事件监听已使用新的导入路径
- [ ] invoke 调用已使用新的导入路径
- [ ] 项目配置文件已更新
- [ ] 构建和运行测试通过

## 5. 测试验证

### 5.1 功能测试
1. 应用启动正常
2. 窗口操作（置顶、标题获取）正常
3. 事件监听（self-event）正常
4. Tauri 命令调用正常
5. 静音功能正常

### 5.2 构建测试
```bash
# 测试各环境构建
npm run build:dev
npm run build:test
npm run build:stage
npm run build:master
```

## 6. 注意事项

1. **后端兼容性**: 确保 Tauri Rust 后端也升级到 v2
2. **类型定义**: 可能需要更新 TypeScript 类型定义
3. **插件兼容性**: 检查第三方 Tauri 插件的 v2 兼容性
4. **权限系统**: Tauri v2 引入了新的权限系统，可能需要配置

## 7. 回滚方案

如果升级后出现问题，可以：
1. 恢复到原 telesale-phone 项目
2. 或者将 package.json 中的版本回退到 `^1.6.0`
3. 撤销所有 API 导入变更

## 8. 参考资源

- [Tauri v2 官方迁移指南](https://tauri.app/start/migrate/from-tauri-1/)
- [Tauri v2 API 参考](https://tauri.app/reference/javascript/api/)
- [Tauri v2 发布说明](https://tauri.app/blog/tauri-2-0/)
