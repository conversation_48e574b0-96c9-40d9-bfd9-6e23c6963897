# telesale-phone-v2

这是 telesale-phone 项目的升级版本，主要目的是将 @tauri-apps/api 包从 v1.6.0 升级到 v2.6.0。

## 主要变更

### 1. 依赖升级
- `@tauri-apps/api`: `^1.6.0` → `^2.6.0`

### 2. API 破坏性更新处理

根据 Tauri v2 的官方迁移指南，以下 API 发生了变更：

#### 2.1 导入路径变更
- `invoke` 和 `event` 相关功能从 `@tauri-apps/api` 迁移到 `@tauri-apps/api/core` 和 `@tauri-apps/api/event`
- 窗口相关功能从 `@tauri-apps/api` 迁移到 `@tauri-apps/api/window`

#### 2.2 具体变更内容

**文件: `src/script/version.ts`**
```diff
- import { invoke, event, window as win } from '@tauri-apps/api'
+ import { invoke } from '@tauri-apps/api/core'
+ import { getCurrentWindow } from '@tauri-apps/api/window'

- const winHandler = win.getCurrent()
+ const winHandler = getCurrentWindow()
```

**文件: `src/store/config.ts`**
```diff
- import { invoke } from '@tauri-apps/api'
+ import { invoke } from '@tauri-apps/api/core'
```

**文件: `src/views/Home/script/connect.ts`**
```diff
- import { invoke, event } from '@tauri-apps/api'
+ import { invoke } from '@tauri-apps/api/core'
```

**文件: `src/views/Home/hooks/useCall.ts`**
```diff
- import { invoke, event } from '@tauri-apps/api'
+ import { invoke } from '@tauri-apps/api/core'
+ import { listen } from '@tauri-apps/api/event'

- unListen = await event.listen('self-event', (e: any) => {
+ unListen = await listen('self-event', (e: any) => {
```

**文件: `src/views/Home/hooks/useMute.ts`**
```diff
- import { invoke } from '@tauri-apps/api'
+ import { invoke } from '@tauri-apps/api/core'
```

**文件: `src/views/Home/hooks/useDing.ts`**
```diff
- import { window as win } from '@tauri-apps/api'
+ import { getCurrentWindow } from '@tauri-apps/api/window'

- await win.appWindow.setAlwaysOnTop(!isDing.value)
+ const appWindow = getCurrentWindow()
+ await appWindow.setAlwaysOnTop(!isDing.value)
```

### 3. 配置文件更新

**文件: `package.json`**
- 项目名称: `telesale-phone` → `telesale-phone-v2`
- @tauri-apps/api 版本: `^1.6.0` → `^2.6.0`

**文件: `vite.config.ts`**
- baseUrl: `/WH_CRM_v2/telesale-phone` → `/WH_CRM_v2/telesale-phone-v2`

**文件: `src/router/index.ts`**
- history base: `WH_CRM_v2/telesale-phone` → `WH_CRM_v2/telesale-phone-v2`

## 兼容性说明

- 所有原有的业务逻辑保持不变
- Tauri 后端调用接口保持兼容
- 用户界面和交互体验无变化

## 开发指南

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建
```bash
npm run build:master  # 生产环境
npm run build:stage   # 预发环境
npm run build:test    # 测试环境
npm run build         # 开发环境
```

## 注意事项

1. 确保 Tauri 后端也升级到 v2 版本以保持兼容性
2. 如果遇到类型错误，可能需要更新 TypeScript 类型定义
3. 建议在升级前备份原项目

## 参考文档

- [Tauri v2 迁移指南](https://tauri.app/start/migrate/from-tauri-1/)
- [Tauri v2 API 文档](https://tauri.app/reference/javascript/api/)
