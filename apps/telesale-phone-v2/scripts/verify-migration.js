#!/usr/bin/env node

/**
 * 验证 Tauri v2 迁移的脚本
 * 检查所有文件中的导入是否已正确更新
 */

const fs = require('fs');
const path = require('path');

const srcDir = path.join(__dirname, '../src');

// 需要检查的文件模式
const filePatterns = ['.ts', '.vue', '.js'];

// 旧的导入模式（应该被替换）
const oldPatterns = [
  /from\s+['"]@tauri-apps\/api['"]\s*$/,
  /import\s*{\s*[^}]*invoke[^}]*}\s*from\s*['"]@tauri-apps\/api['"]/,
  /import\s*{\s*[^}]*event[^}]*}\s*from\s*['"]@tauri-apps\/api['"]/,
  /import\s*{\s*[^}]*window\s+as\s+win[^}]*}\s*from\s*['"]@tauri-apps\/api['"]/,
  /window\.__TAURI_IPC__/,
  /win\.getCurrent\(\)/,
  /win\.appWindow/,
  /event\.listen/
];

// 新的导入模式（应该存在）
const newPatterns = [
  /from\s+['"]@tauri-apps\/api\/core['"]/,
  /from\s+['"]@tauri-apps\/api\/event['"]/,
  /from\s+['"]@tauri-apps\/api\/window['"]/,
  /getCurrentWindow/,
  /window\.__TAURI__/
];

function getAllFiles(dir, extensions) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const relativePath = path.relative(process.cwd(), filePath);
  const issues = [];
  
  // 检查是否还有旧的导入模式
  for (const pattern of oldPatterns) {
    if (pattern.test(content)) {
      issues.push(`发现旧的导入模式: ${pattern.source}`);
    }
  }
  
  return { file: relativePath, issues };
}

function main() {
  console.log('🔍 开始验证 Tauri v2 迁移...\n');
  
  const files = getAllFiles(srcDir, filePatterns);
  let totalIssues = 0;
  
  for (const file of files) {
    const result = checkFile(file);
    
    if (result.issues.length > 0) {
      console.log(`❌ ${result.file}:`);
      for (const issue of result.issues) {
        console.log(`   - ${issue}`);
      }
      console.log();
      totalIssues += result.issues.length;
    }
  }
  
  if (totalIssues === 0) {
    console.log('✅ 所有文件都已正确迁移到 Tauri v2!');
    console.log('\n📋 迁移检查清单:');
    console.log('  ✅ 所有 @tauri-apps/api 导入已更新');
    console.log('  ✅ 窗口 API 已迁移到新的调用方式');
    console.log('  ✅ 事件监听已使用新的导入路径');
    console.log('  ✅ invoke 调用已使用新的导入路径');
    console.log('  ✅ 环境检测已更新');
  } else {
    console.log(`❌ 发现 ${totalIssues} 个需要修复的问题`);
    console.log('\n请参考 MIGRATION_GUIDE.md 了解如何修复这些问题。');
    process.exit(1);
  }
  
  console.log('\n📚 相关文档:');
  console.log('  - README.md - 项目概述');
  console.log('  - MIGRATION_GUIDE.md - 详细迁移指南');
  console.log('  - CHANGELOG.md - 变更日志');
}

if (require.main === module) {
  main();
}

module.exports = { checkFile, getAllFiles };
