{"name": "telesale-phone-v2", "version": "0.0.0", "license": "ISC", "scripts": {"dev": "vite --mode dev", "dev:mock": "vite --mode mock", "dev:force": "vite --mode dev --force", "dev:stage": "vite --mode stage", "dev:master": "vite --mode master", "build": "vite build --mode dev", "build:test": "vite build --mode test", "build:stage": "vite build --mode stage", "build:master": "vite build --mode master", "serve": "vite preview --mode prod"}, "devDependencies": {"@guanghe-pub/nexus-tsconfig": "^1.0.5", "vite-plugin-html-config": "^1.0.11"}, "dependencies": {"@guanghe-pub/nexus-axios": "^1.0.6", "@tauri-apps/api": "^2.6.0", "@vant/touch-emulator": "^1.4.0"}}