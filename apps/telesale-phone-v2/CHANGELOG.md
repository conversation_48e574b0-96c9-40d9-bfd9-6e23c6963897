# 变更日志

## [2.0.0] - 2024-12-19

### 新增
- 升级 @tauri-apps/api 从 v1.6.0 到 v2.6.0
- 新增详细的迁移指南文档
- 新增项目 README 文档

### 变更
- **破坏性变更**: 更新所有 Tauri API 导入路径
  - `invoke` 从 `@tauri-apps/api` 迁移到 `@tauri-apps/api/core`
  - `listen` 从 `@tauri-apps/api` 迁移到 `@tauri-apps/api/event`
  - 窗口相关 API 从 `@tauri-apps/api` 迁移到 `@tauri-apps/api/window`

### 修复
- 更新窗口检测逻辑从 `window.__TAURI_IPC__` 到 `window.__TAURI__`
- 修复窗口操作 API 调用方式

### 文件变更详情

#### 配置文件
- `package.json`: 项目名称更新为 `telesale-phone-v2`，依赖版本升级
- `vite.config.ts`: baseUrl 更新为 `/WH_CRM_v2/telesale-phone-v2`
- `src/router/index.ts`: 路由 base 更新为 `WH_CRM_v2/telesale-phone-v2`

#### 源代码文件
- `src/script/version.ts`: 
  - 导入变更: `invoke, event, window as win` → `invoke` + `getCurrentWindow`
  - API 调用变更: `win.getCurrent()` → `getCurrentWindow()`

- `src/store/config.ts`:
  - 导入变更: `invoke` 从 `@tauri-apps/api` → `@tauri-apps/api/core`

- `src/views/Home/script/connect.ts`:
  - 导入变更: `invoke, event` → `invoke`

- `src/views/Home/hooks/useCall.ts`:
  - 导入变更: `invoke, event` → `invoke` + `listen`
  - API 调用变更: `event.listen` → `listen`

- `src/views/Home/hooks/useMute.ts`:
  - 导入变更: `invoke` 从 `@tauri-apps/api` → `@tauri-apps/api/core`

- `src/views/Home/hooks/useDing.ts`:
  - 导入变更: `window as win` → `getCurrentWindow`
  - API 调用变更: `win.appWindow.setAlwaysOnTop` → `getCurrentWindow().setAlwaysOnTop`

- `src/views/Home/index.vue`:
  - 环境检测变更: `window.__TAURI_IPC__` → `window.__TAURI__`

### 兼容性说明
- 保持所有业务逻辑不变
- Tauri 后端调用接口保持兼容
- 用户界面和交互体验无变化

### 升级指南
详细的升级指南请参考 `MIGRATION_GUIDE.md` 文件。

### 注意事项
1. 确保 Tauri 后端也升级到 v2 版本
2. 如遇到类型错误，可能需要更新 TypeScript 类型定义
3. 建议在升级前备份原项目

### 参考资源
- [Tauri v2 迁移指南](https://tauri.app/start/migrate/from-tauri-1/)
- [Tauri v2 API 文档](https://tauri.app/reference/javascript/api/)
- [Tauri v2 发布说明](https://tauri.app/blog/tauri-2-0/)
