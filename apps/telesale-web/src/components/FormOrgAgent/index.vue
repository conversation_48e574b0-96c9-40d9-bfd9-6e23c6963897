<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";
import { findMember, findMembersApi } from "/@/api/agent";
import findOrganizationMath from "/@/utils/asyn/findOrganization";

interface Props {
  form: any;
  multiple?: boolean;
  workerId?: string;
  orgId?: string;
  agentListKey?: string; //与useUserStoreHook里面的坐席列表key保持一致
  isAloneAgent?: boolean; //是否不受叶子节点的限制展示坐席列表
  limitName?: string; //组织架构的展示是否受权限控制
  isAllOrg?: boolean; //组织机构的数据是全量还是按登录人员的组织机构来
  showOrg?: boolean;
  placeholderOrg?: string;
  placeholderWorker?: string;
}
interface Emits {
  (e: "update:form", val: any): void;
}
const props = withDefaults(defineProps<Props>(), {
  workerId: "workerId",
  orgId: "orgId",
  agentListKey: "agentList",
  limitName: "defualt-no-this-limit",
  isAllOrg: false,
  isAloneAgent: false,
  multiple: false,
  showOrg: false,
  placeholderOrg: "请选择小组",
  placeholderWorker: "请选择坐席"
});
const emit = defineEmits<Emits>();

const form = computed({
  get() {
    return props.form;
  },
  set(val: any) {
    emit("update:form", val);
  }
});

let agentList = ref([]);
function agentListReset() {
  agentList.value = useUserStoreHook()[props.agentListKey];
}
agentListReset();

const propsParams = {
  value: "id",
  label: "name",
  checkStrictly: true,
  emitPath: false,
  multiple: props.multiple
};
const cascaderRefs = ref();
function changeOrg(data) {
  form.value[props.workerId] = undefined;
  if (props.multiple && !data?.length) {
    return agentListReset();
  }

  if (data) {
    findMemberMath(data);
  } else {
    agentListReset();
  }

  if (!props.multiple) {
    cascaderRefs.value.togglePopperVisible(false);
  }
}

function findMemberMath(id) {
  const fn = props.multiple ? findMembersApi : findMember;
  fn({ [props.multiple ? "ids" : "id"]: id }).then(
    ({ data }: { data: any }) => {
      //根据agentListKey前缀job判断需不需要带离职信息
      if (props.agentListKey.indexOf("job") < 0) {
        agentList.value = data.list.map(item => {
          item.status === 2 && (item.name += "（已离职）");
          item.label = item.name;
          item.value = item.id;
          return item;
        });
      } else {
        agentList.value = data.list.filter(item => {
          item.label = item.name;
          item.value = item.id;
          return item.status !== 2;
        });
      }
    }
  );
}

let orgData = ref([]);

onMounted(async () => {
  if (!props.isAllOrg) {
    orgData.value = useUserStoreHook().orgData;
  } else {
    orgData.value = await findOrganizationMath();
  }
});

defineExpose({
  agentListReset
});
</script>
<template>
  <el-form-item
    v-if="
      (useUserStoreHook().authorizationMap.indexOf(limitName) > -1 &&
        useUserStoreHook().userMsg.leafNode) ||
      props.showOrg
    "
    :prop="props.multiple ? 'orgIdList' : props.orgId"
  >
    <el-cascader
      v-model="form[props.multiple ? 'orgIdList' : props.orgId]"
      :options="orgData"
      :props="propsParams"
      filterable
      clearable
      @change="changeOrg"
      :show-all-levels="false"
      :placeholder="props.placeholderOrg"
      ref="cascaderRefs"
      collapse-tags
      collapse-tags-tooltip
      :max-collapse-tags="1"
      :class="[props.multiple ? 'multiple' : '']"
    />
  </el-form-item>
  <el-form-item
    :prop="workerId"
    v-if="isAloneAgent || useUserStoreHook().userMsg.leafNode"
  >
    <el-select-v2
      v-model="form[workerId]"
      filterable
      clearable
      :options="agentList"
      :placeholder="props.placeholderWorker"
    />
  </el-form-item>
</template>

<style scoped lang="scss">
:deep(.multiple) {
  .el-input {
    width: 240px !important;
  }
}
</style>
