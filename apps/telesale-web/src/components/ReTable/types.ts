/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-06-09 10:53:39
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-06-09 16:17:48
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/components/ReTable/types.ts
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import type { VNodeChild } from "vue";

export interface FilterOptions {
  rowKey?: string;
  columns: any; // 筛选数据
  isMultiple?: boolean; // 默认单选
  defaultFilterValue?: any; // 默认值
  label?: string; // 筛选数据的label值，默认label
  value?: string; // 筛选数据的value值，默认value
}

export interface RecordProps<T extends object> {
  text: any;
  row: T;
  index: number;
  column: TableObj<T> | CardTableObj<T>;
}

export interface ColumnSlot {
  name: string;
}

interface EleSet {
  referProp?: string;
  val?: string;
  sty?: string;
}

export type TableObj<T extends object = any> = {
  desc: string; //对应表格显示的标题label
  field: keyof T & string; //对应表格显示的字段名称props
  fixed?: boolean | unknown; //是否固定左侧
  sortable?: boolean | unknown; //对应列是否可以排序
  minWidth?: number | unknown; //对应列的最小宽度
  filtersList?: any[] | object | unknown; //数据过滤的选项
  showTip?: boolean | unknown; //当内容过长被隐藏时显示 tooltip
  filteredValue?: number | unknown; //选中的数据过滤项
  headerTip?: boolean; // 是否显示tooltip
  headerTipTitle?: string; // 显示tooltipn内容
  event?: string; //column事件名称
  htmlChange?: undefined | boolean | unknown; //是否直接挂载dom
  addType?: "up" | "down"; // 新增元素的位置
  typeChange?: any[] | object | unknown; //值转换
  eleSet?: EleSet | any; //涉及到样式变化
  filters?: Function | unknown; //column展示数据的自定义函数
  idTransfer?: string | unknown; //坐席Id转换成name
  idName?: string | unknown; //坐席默认值，如果全局接口workerId和workid统一，可去掉
  timeChange?: number | unknown; //时间格式转换
  customRender?: (record: RecordProps<T>) => VNodeChild | VNodeChild; //自定义渲染函数
  slot?: ColumnSlot;
  isShow?: boolean | Function;
  isCopy?: boolean; // 是否可以复制
  // 表头筛选配置项
  filterOptions?: FilterOptions;
  filterCascaderOptions?: FilterOptions;
  isFamily?: boolean; // 是否是家庭ID弹窗
  isTimeSort?: boolean; // 是否是时间排序
};

export type CardTableObj<T extends object = any> = {
  desc?: string;
  title?: string;
  field: keyof T & string;
  showTip?: boolean;
  event?: string;
  htmlChange?: undefined | boolean;
  addType?: "up" | "down"; // 新增元素的位置
  typeChange?: any[] | object;
  eleSet?: EleSet | any;
  filters?: Function;
  idTransfer?: string;
  idName?: string;
  timeChange?: number;
  span?: number | undefined;
  customRender?: (record: RecordProps<T>) => VNodeChild | VNodeChild; //自定义渲染函数
  slot?: ColumnSlot;
  isShow?: boolean | Function;
  isCopy?: boolean; // 是否可以复制
};

export type TableColumns<T extends object = any> = CardTableObj<T> &
  TableObj<T>;

export type OperationObj = {
  isShow?: undefined | boolean | Function; //是否展示
  popconfirm?: string; //是否采用popconfirm展示操作
  event?: string; //按钮事件名称
  text: string; //按钮名称
  eventFn?: Function; // 按钮事件
  customRender?: (record: any) => VNodeChild | VNodeChild; //自定义渲染函数
  slot?: ColumnSlot;
  disabled?: Function;
};
