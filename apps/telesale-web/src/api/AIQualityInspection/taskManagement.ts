/*
 * @Description  : 任务管理API
 *
 * ## 变更记录
 * - 2025-06-27: 新增WorkerTrainingTaskMessageExtra接口，添加extra字段支持超时状态标记
 * - 2025-06-27: 更新WorkerTrainingTaskMessage接口，新增extra字段
 * - 2025-06-25: 严格按照API规范同步finishWorkerTrainingTaskCourse接口，移除不存在的字段
 * - 2025-06-25: 移除speechTranscriptionCompleted、waitingForTranscription等API规范中不存在的字段
 */

import { http } from "/@/utils/http";
import baseURL from "/@/api/url";
import { TrainingCourseInstance } from "./courseManage";

/**
 * 任务状态枚举
 */
export const TASK_STATUS = {
  CREATE: "create", // 未开始
  PROCESS: "process", // 进行中
  END: "end" // 已完成
};

/**
 * 员工训练任务课程实例
 */
export interface WorkerTrainingTaskCourseInstance {
  /** 员工训练任务ID */
  workerTrainingTaskId?: string;
  /** 任务具体的课程ID */
  trainingTaskCourseId?: string;
  /** 是否完成 */
  finished?: boolean;
  /** 完成时间 */
  finishedAt?: string;
  /** 评级 */
  score?: string;
  /** 训练会话ID */
  workerTrainingConversationId?: string;
  /** 课程信息 */
  course?: TrainingTaskCourseInstance;
}

/**
 * 员工训练任务实例
 */
export interface WorkerTrainingTaskInstance {
  /** ID */
  id?: number;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 任务接收者 */
  workerId?: number;
  /** 是否完成 */
  finished?: boolean;
  /** 完成时间 */
  finishedAt?: string;
  /** 训练任务ID */
  trainingTaskId?: number;
  /** 任务信息 */
  task?: TrainingTaskInstance;
  /** 课程列表 */
  courses?: WorkerTrainingTaskCourseInstance[];
  /** 课程数量 */
  courseNum?: number;
  /** 完成数量 */
  finishNum?: number;
  /** 任务接受者姓名 */
  workerName?: string;
}

/**
 * 员工训练任务列表查询参数
 */
export interface WorkerTrainingTaskQueryParams {
  /** 页码（从1开始） */
  pages?: string;
  /** 每页数量 */
  pageSize?: string;
  /** 员工ID */
  workerId?: number;
  /** 任务名称关键词筛选（可选） */
  nameKeyword?: string;
  /** 按状态筛选（可选） */
  statusFilter?: string;
}

/**
 * 员工训练任务列表响应
 */
export interface WorkerTrainingTaskListReply {
  /** 任务列表 */
  items?: WorkerTrainingTaskInstance[];
  /** 总记录数 */
  total?: string;
}

/**
 * 员工训练任务详情响应
 */
export interface WorkerTrainingTaskInfoReply {
  /** 任务信息 */
  item?: WorkerTrainingTaskInstance;
}

/**
 * 员工训练任务更新响应
 */
export interface WorkerTrainingTaskUpdateReply {}

/**
 * 训练任务列表查询参数
 */
export interface TrainingTaskQueryParams {
  /** 页码（从1开始） */
  pages?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 任务名称关键词筛选（可选） */
  nameKeyword?: string;
  /** 按状态筛选（可选） */
  statusFilter?: string;
  /** 按创建人ID筛选（可选） */
  workerIdFilter?: number;
}

/**
 * 训练任务课程实例
 */
export interface TrainingTaskCourseInstance {
  /** ID */
  id: number;
  /** 训练课程ID */
  trainingCourseId: number;
  /** 训练任务ID */
  trainingTaskId: number;
  /** 权重 */
  score: number;
  /** 课程信息 */
  course?: TrainingCourseInstance;
}

/**
 * 训练任务实例
 */
export interface TrainingTaskInstance {
  /** ID */
  id: number;
  /** 任务名称（最大20字符） */
  name: string;
  /** 任务状态 */
  status: string;
  /** 创建人ID */
  workerId: number;
  /** 操作人（最大10字符） */
  operator: string;
  /** 开始时间 */
  beginAt: string;
  /** 结束时间 */
  endAt: string;
  /** 创建时间 */
  createdAt: string;
  /** 更新时间 */
  updatedAt: string;
  /** 课程列表 */
  courses: TrainingTaskCourseInstance[];
  /** 任务受理人 */
  acceptor?: number[];
}

/**
 * 训练任务列表响应
 */
export interface TrainingTaskListReply {
  /** 任务列表 */
  tasks: TrainingTaskInstance[];
  /** 总记录数 */
  total: string;
}

/**
 * 训练任务创建/更新请求参数
 */
export interface TrainingTaskCreateUpdateRequest {
  /** ID */
  id?: number;
  /** 任务名称（最大20字符） */
  name: string;
  /** 任务状态 */
  status?: string;
  /** 创建人ID */
  workerId: number;
  /** 操作人（最大10字符） */
  operator: string;
  /** 开始时间 */
  beginAt: string;
  /** 结束时间 */
  endAt: string;
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** 课程列表 */
  courses: TrainingTaskCourseInstance[];
  /** 任务受理人 */
  acceptor?: number[];
}

/**
 * 训练任务详情响应
 */
export interface TrainingTaskInfoReply {
  /** 任务信息 */
  task: TrainingTaskInstance;
}

/**
 * 训练任务完成请求参数
 */
export interface TrainingTaskFinishRequest {
  /** 任务ID */
  id: number;
  operator: string;
  workerId: string;
}

/**
 * 训练任务完成情况响应
 */
export interface TrainingTaskProcessReply {
  items: {
    workerId: number; // 坐席id
    courseNum: number; // 当前坐席当前任务下的全部课程数量
    finishNum: number; // 当前坐席当前任务下已完成的课程数量
    workerName: string; // 坐席姓名
    groupName2: string; // 第三级
    groupName3: string; // 第四级
    groupName4: string; // 第五级
  };
}

/**
 * 获取训练任务列表
 * @param params 查询参数
 * @returns Promise<TrainingTaskListReply>
 */
export function getTrainingTaskList(params: TrainingTaskQueryParams) {
  return http.get<any, TrainingTaskListReply>(
    `${baseURL.robot}/admin/trainingTask`,
    { params }
  );
}

/**
 * 创建训练任务
 * @param data 任务信息
 * @returns Promise<void>
 */
export function createTrainingTask(data: TrainingTaskCreateUpdateRequest) {
  return http.request("put", `${baseURL.robot}/admin/trainingTask`, {
    data
  });
}

/**
 * 获取训练任务详情
 * @param id 任务ID
 * @returns Promise<TrainingTaskInfoReply>
 */
export function getTrainingTaskInfo(id: number) {
  return http.get<any, TrainingTaskInfoReply>(
    `${baseURL.robot}/admin/trainingTask/${id}`
  );
}

/**
 * 更新训练任务
 * @param id 任务ID
 * @param data 任务信息
 * @returns Promise<void>
 */
export function updateTrainingTask(
  id: number,
  data: TrainingTaskCreateUpdateRequest
) {
  return http.post<any, void>(`${baseURL.robot}/admin/trainingTask/${id}`, {
    data
  });
}

/**
 * 完成训练任务
 * @param data 任务完成请求参数
 * @returns Promise<void>
 */
export function finishTrainingTask(data: TrainingTaskFinishRequest) {
  return http.post<any, void>(`${baseURL.robot}/admin/trainingTask/finish`, {
    data
  });
}

/**
 * 获取员工训练任务列表
 * @param params 查询参数
 * @returns Promise<WorkerTrainingTaskListReply>
 */
export function getWorkerTrainingTaskList(
  params: WorkerTrainingTaskQueryParams
) {
  return http.get<any, WorkerTrainingTaskListReply>(
    `${baseURL.robot}/admin/workerTrainingTask`,
    { params }
  );
}

/**
 * 获取员工训练任务详情
 * @param id 任务ID
 * @returns Promise<WorkerTrainingTaskInfoReply>
 */
export function getWorkerTrainingTaskInfo(id: number) {
  return http.get<any, WorkerTrainingTaskInfoReply>(
    `${baseURL.robot}/admin/workerTrainingTask/${id}`
  );
}

/**
 * 更新员工训练任务
 * @param id 任务ID
 * @returns Promise<WorkerTrainingTaskUpdateReply>
 */
export function updateWorkerTrainingTask(id: number) {
  return http.post<any, WorkerTrainingTaskUpdateReply>(
    `${baseURL.robot}/admin/workerTrainingTask/${id}`,
    {}
  );
}

/**
 * 获取训练任务完成情况
 * @param trainingTaskId 训练任务ID
 * @returns Promise<TrainingTaskProcessReply>
 */
export function getTrainingTaskProcess(trainingTaskId: number) {
  return http.get<any, TrainingTaskProcessReply>(
    `${baseURL.robot}/admin/workerTrainingTask/${trainingTaskId}/process`
  );
}

/**
 * 课程对话请求参数
 */
export interface WorkerTrainingTaskCourseChatRequest {
  /** 员工训练任务ID */
  workerTrainingTaskId: number;
  /** 任务具体的课程ID */
  trainingTaskCourseId: number;
  /** 问题 */
  question: string;
  /** 对话上下文 */
  chatContext: {
    /** 角色 */
    role: string;
    /** 内容 */
    content: string;
  }[];
}

/**
 * 课程对话响应
 */
export interface WorkerTrainingTaskCourseChatReply {}

/**
 * 案例评级实例 (对应 excellentcaseCaseAppraiseInstance)
 */
export interface CaseAppraiseInstance {
  /** 评级的分数 */
  score?: string;
  /** 评级的结果 */
  result?: string;
  /** 评级时间 */
  createdAt?: string;
  /** 原因/是否通过 */
  reason?: string;
}

/**
 * 员工训练任务消息扩展信息 (对应 workertrainingtaskcourseWorkerTrainingTaskMessageExtra)
 * @version API规范版本: 1.0.0
 * @lastSync 2025-06-27
 * @changes 新增extra字段相关类型定义
 */
export interface WorkerTrainingTaskMessageExtra {
  /** 销售对话是否超时 */
  timeout?: boolean;
}

/**
 * 员工训练任务消息实例 (对应 workertrainingtaskcourseWorkerTrainingTaskMessage)
 * @version API规范版本: 1.0.0
 * @lastSync 2025-06-27
 * @changes 新增extra字段
 */
export interface WorkerTrainingTaskMessage {
  /** 角色 user or worker */
  role?: string;
  /** 内容 */
  content?: string;
  /** 开始时间 秒级时间戳 */
  start?: string;
  /** 创建时间 */
  createdAt?: string;
  /** 唯一标识 msgid */
  key?: string;
  /** 扩展信息 */
  extra?: WorkerTrainingTaskMessageExtra;
}

/**
 * 员工训练会话实例 (对应 workertrainingtaskcourseWorkerTrainingConversationInstance)
 */
export interface WorkerTrainingConversationInstance {
  /** 会话ID */
  id?: string;
  /** 员工ID */
  workerId?: number; // API规范：integer (int64)
  /** 训练任务课程ID */
  trainingTaskCourseId?: string; // API规范：string (int64)
  /** 训练时长 */
  duration?: number; // API规范：integer (int32)
  /** 消息列表 */
  messages?: WorkerTrainingTaskMessage[];
  /** 开始时间 */
  begin?: string; // API规范：string (int64)
  /** 结束时间 */
  end?: string; // API规范：string (int64)
  /** 创建时间 */
  createdAt?: string;
  /** 更新时间 */
  updatedAt?: string;
  /** AI评价 */
  aiAppraise?: CaseAppraiseInstance;
  /** 合成语音 */
  mergeAudioURL?: string;
  /** 训练模式 audio text */
  trainingMode?: string;
}

/**
 * 员工训练任务课程详情请求参数 (对应 workertrainingtaskcourseWorkerTrainingTaskCourseInfoRequest)
 */
export interface WorkerTrainingTaskCourseInfoRequest {
  /** 员工训练任务ID */
  workerTrainingTaskId?: string; // API规范：string (int64)
  /** 任务具体的课程ID */
  trainingTaskCourseId?: string; // API规范：string (int64)
}

/**
 * 员工训练任务课程详情响应
 */
export interface WorkerTrainingTaskCourseInfoReply {
  conversation?: WorkerTrainingConversationInstance;
}

/**
 * 发送TTS - 流式接收音频数据
 * @param data TTS请求参数
 * @returns Promise<ArrayBuffer> 返回拼接后的音频数据
 */
export function sendTts(data: {
  text: string;
  role: string;
  msgid: string;
}): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    console.log("发送TTS请求:", data);

    // 获取认证头部
    const ShadowAuthorization = localStorage.getItem(
      "whcrmShadowAuthorization"
    );
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "x-enabled-behavior": "true"
    };

    if (ShadowAuthorization) {
      headers["ShadowAuthorization"] = ShadowAuthorization;
    }

    console.log("TTS请求头部:", headers);

    // 使用 fetch 处理流式音频响应
    fetch(`${baseURL.robot}/admin/audio/tts`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        text: data.text,
        role: data.role,
        msgid: data.msgid
      })
    })
      .then(response => {
        console.log("TTS响应状态:", response.status, response.statusText);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
          throw new Error("Response body is null");
        }

        const reader = response.body.getReader();
        const chunks: Uint8Array[] = [];
        let totalBytesReceived = 0;

        // 读取流式音频数据
        function readStream(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              console.log(
                "TTS流式接收完成，总共接收:",
                totalBytesReceived,
                "bytes，数据块数量:",
                chunks.length
              );

              // 流结束，拼接所有音频数据块
              const totalLength = chunks.reduce(
                (sum, chunk) => sum + chunk.length,
                0
              );
              const result = new Uint8Array(totalLength);
              let offset = 0;

              for (const chunk of chunks) {
                result.set(chunk, offset);
                offset += chunk.length;
              }

              console.log(
                "TTS音频数据拼接完成，最终大小:",
                result.buffer.byteLength,
                "bytes"
              );

              // 返回 ArrayBuffer
              resolve(result.buffer);
              return;
            }

            // 收集音频数据块
            if (value) {
              chunks.push(value);
              totalBytesReceived += value.length;
              console.log(
                "接收到TTS数据块:",
                value.length,
                "bytes，累计:",
                totalBytesReceived,
                "bytes"
              );
            }

            // 继续读取
            return readStream();
          });
        }

        return readStream();
      })
      .catch(error => {
        console.error("TTS stream processing error:", error);
        reject(error);
      });
  });
}

/**
 * 发送TTS - 流式接收音频数据并立即播放
 * @param data TTS请求参数
 * @param onAudioChunk 音频数据块回调函数
 * @returns Promise<ArrayBuffer> 返回拼接后的完整音频数据
 */
export function sendTtsStream(
  data: {
    text: string;
    role: string;
    msgid: string;
  },
  onAudioChunk?: (chunk: ArrayBuffer) => void
): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    console.log("发送流式TTS请求:", data);

    // 获取认证头部
    const ShadowAuthorization = localStorage.getItem(
      "whcrmShadowAuthorization"
    );
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "x-enabled-behavior": "true"
    };

    if (ShadowAuthorization) {
      headers["ShadowAuthorization"] = ShadowAuthorization;
    }

    console.log("流式TTS请求头部:", headers);

    // 使用 fetch 处理流式音频响应
    fetch(`${baseURL.robot}/admin/audio/tts`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        text: data.text,
        role: data.role,
        msgid: data.msgid
      })
    })
      .then(response => {
        console.log("流式TTS响应状态:", response.status, response.statusText);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
          throw new Error("Response body is null");
        }

        const reader = response.body.getReader();
        const chunks: Uint8Array[] = [];
        let totalBytesReceived = 0;

        // 读取流式音频数据
        function readStream(): Promise<void> {
          return reader.read().then(({ done, value }) => {
            if (done) {
              console.log(
                "流式TTS接收完成，总共接收:",
                totalBytesReceived,
                "bytes，数据块数量:",
                chunks.length
              );

              // 流结束，拼接所有音频数据块
              const totalLength = chunks.reduce(
                (sum, chunk) => sum + chunk.length,
                0
              );
              const result = new Uint8Array(totalLength);
              let offset = 0;

              for (const chunk of chunks) {
                result.set(chunk, offset);
                offset += chunk.length;
              }

              console.log(
                "流式TTS音频数据拼接完成，最终大小:",
                result.buffer.byteLength,
                "bytes"
              );

              // 返回 ArrayBuffer
              resolve(result.buffer);
              return;
            }

            // 收集音频数据块
            if (value) {
              chunks.push(value);
              totalBytesReceived += value.length;
              console.log(
                "接收到流式TTS数据块:",
                value.length,
                "bytes，累计:",
                totalBytesReceived,
                "bytes"
              );

              // 立即回调处理音频数据块
              if (onAudioChunk && value.length > 0) {
                try {
                  // 参考saler-robot-web的实现，直接使用value.buffer
                  const chunkBuffer = value.buffer as ArrayBuffer;
                  onAudioChunk(chunkBuffer);
                } catch (chunkError) {
                  console.warn("处理音频数据块时出错:", chunkError);
                }
              }
            }

            // 继续读取
            return readStream();
          });
        }

        return readStream();
      })
      .catch(error => {
        console.error("流式TTS stream processing error:", error);
        reject(error);
      });
  });
}

/**
 * 课程对话
 * @param data 请求参数
 * @returns Promise<WorkerTrainingTaskCourseChatReply>
 */
export function workerTrainingTaskCourseChat(
  data: WorkerTrainingTaskCourseChatRequest
) {
  return http.post<any, WorkerTrainingTaskCourseChatReply>(
    `${baseURL.robot}/admin/workerTrainingTaskCourse/chat`,
    { data },
    {
      headers: {
        "Content-Type": "application/json",
        Accept: "text/event-stream",
        "Cache-Control": "no-cache"
      }
    }
  );
}

/**
 * 课程对话 - 流式响应版本
 * @param data 请求参数
 * @param onChunk 流式数据处理回调
 * @returns Promise<void>
 */
export function workerTrainingTaskCourseChatStream(
  data: WorkerTrainingTaskCourseChatRequest,
  onChunk: (chunk: string) => void
): Promise<void> {
  return new Promise((resolve, reject) => {
    // 获取认证头部
    const ShadowAuthorization = localStorage.getItem(
      "whcrmShadowAuthorization"
    );
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      Accept: "text/event-stream",
      "Cache-Control": "no-cache",
      "x-enabled-behavior": "true"
    };

    if (ShadowAuthorization) {
      headers["ShadowAuthorization"] = ShadowAuthorization;
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 120000); // 2分钟超时

    // 使用 fetch 处理流式响应，直接发送参数对象
    fetch(`${baseURL.robot}/admin/workerTrainingTaskCourse/chat`, {
      method: "POST",
      headers,
      body: JSON.stringify(data), // 直接发送参数对象，不使用 { data } 包装
      signal: controller.signal
    })
      .then(response => {
        // 清除超时定时器
        clearTimeout(timeoutId);

        if (!response.ok) {
          // 根据状态码提供更具体的错误信息
          let errorMessage = `HTTP error! status: ${response.status}`;
          if (response.status === 401) {
            errorMessage = "认证失败，请重新登录";
          } else if (response.status === 403) {
            errorMessage = "权限不足，请联系管理员";
          } else if (response.status === 404) {
            errorMessage = "服务接口不存在";
          } else if (response.status >= 500) {
            errorMessage = "服务器内部错误，请稍后重试";
          } else if (response.status === 408 || response.status === 504) {
            errorMessage = "请求超时，请检查网络连接";
          }
          throw new Error(errorMessage);
        }

        if (!response.body) {
          throw new Error("Response body is null");
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = "";

        // 读取流式数据
        function readStream(): Promise<void> {
          return reader
            .read()
            .then(({ done, value }) => {
              if (done) {
                // 流结束，处理最后的数据
                if (buffer.trim()) {
                  onChunk(buffer);
                }
                resolve();
                return;
              }

              // 解码数据块
              const chunk = decoder.decode(value, { stream: true });
              buffer += chunk;

              // 处理完整的行
              const lines = buffer.split("\n");
              buffer = lines.pop() || ""; // 保留最后一个不完整的行

              for (const line of lines) {
                if (line.trim()) {
                  onChunk(line);

                  // 检查是否为结束标记
                  const trimmedLine = line.trim();
                  if (
                    trimmedLine === "data: [DONE]" ||
                    trimmedLine === "[DONE]" ||
                    trimmedLine === "data: EOF" ||
                    trimmedLine === "EOF"
                  ) {
                    console.log("检测到流式接口结束标记:", trimmedLine);
                    resolve();
                    return;
                  }
                }
              }

              // 继续读取
              return readStream();
            })
            .catch(error => {
              // 处理读取过程中的错误
              if (error.name === "AbortError") {
                throw new Error("请求超时，请检查网络连接或稍后重试");
              }
              throw error;
            });
        }

        return readStream();
      })
      .catch(error => {
        // 清除超时定时器
        clearTimeout(timeoutId);

        console.error("Stream processing error:", error);

        // 提供更友好的错误信息
        let friendlyMessage = "网络异常或调用超时";
        if (error.name === "AbortError") {
          friendlyMessage = "请求超时，请检查网络连接或稍后重试";
        } else if (error.message.includes("Failed to fetch")) {
          friendlyMessage = "网络连接失败，请检查网络状态";
        } else if (error.message.includes("HTTP error")) {
          friendlyMessage = error.message;
        }

        // 创建一个包含友好错误信息的错误对象
        const friendlyError = new Error(friendlyMessage) as Error & {
          originalError?: any;
        };
        friendlyError.originalError = error;
        reject(friendlyError);
      });
  });
}

/**
 * 获取员工训练任务课程详情
 * @param data 请求参数
 * @returns Promise<WorkerTrainingTaskCourseInfoReply>
 */
export function getWorkerTrainingTaskCourseInfo(
  data: WorkerTrainingTaskCourseInfoRequest
) {
  return http.post<any, WorkerTrainingTaskCourseInfoReply>(
    `${baseURL.robot}/admin/workerTrainingTaskCourse`,
    { data }
  );
}

/**
 * 员工训练任务课程完成请求参数 (旧版本)
 */
export interface WorkerTrainingTaskCourseFinishOldRequest {
  /** 员工训练任务ID */
  workerTrainingTaskId: number;
  /** 任务具体的课程ID */
  trainingTaskCourseId: number;
  /** 评级分数 */
  score?: string;
  /** 操作人 */
  operator?: string;
  /** 员工ID */
  workerId?: number;
}

/**
 * 员工训练任务课程更新请求参数 (对应 workertrainingtaskcourseWorkerTrainingTaskUpdateRequest)
 * @version API规范版本: 1.0.0
 * @lastSync 2025-06-25
 * @changes 严格按照API规范同步，移除不存在的字段
 */
export interface WorkerTrainingTaskCourseUpdateRequest {
  /** 员工训练任务ID */
  workerTrainingTaskId?: string; // API规范：string (int64)
  /** 任务具体的课程ID */
  trainingTaskCourseId?: string; // API规范：string (int64)
  /** 会话详情 */
  conversation?: WorkerTrainingConversationInstance;
  /** 训练模式 audio text */
  trainingMode?: string;
}

/**
 * 员工训练任务课程完成响应 (对应 workertrainingtaskcourseWorkerTrainingTaskUpdateReply)
 */
export interface WorkerTrainingTaskCourseFinishReply {}

/**
 * 检查违禁词请求参数
 */
export interface WorkerTrainingTaskCourseCheckRequest {
  /** 需要检查的问题内容 */
  question: string;
}

/**
 * 检查违禁词响应
 */
export interface WorkerTrainingTaskCourseCheckReply {}

/**
 * 完成员工训练任务课程 (实际是更新课程详情)
 * @param data 课程更新请求参数
 * @returns Promise<WorkerTrainingTaskCourseFinishReply>
 */
export function finishWorkerTrainingTaskCourse(
  data: WorkerTrainingTaskCourseUpdateRequest
) {
  return http.post<any, WorkerTrainingTaskCourseFinishReply>(
    `${baseURL.robot}/admin/workerTrainingTaskCourse/finish`,
    { data }
  );
}

/**
 * 检查是否有违禁词
 * @param data 检查请求参数
 * @returns Promise<WorkerTrainingTaskCourseCheckReply>
 */
export function checkWorkerTrainingTaskCourse(
  data: WorkerTrainingTaskCourseCheckRequest
) {
  return http.post<any, WorkerTrainingTaskCourseCheckReply>(
    `${baseURL.robot}/admin/workerTrainingTaskCourse/check`,
    { data }
  );
}
