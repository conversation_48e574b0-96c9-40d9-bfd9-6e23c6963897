/*
 * @Date         : 2025-04-25 11:35:56
 * @Description  : 优秀案例库API
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { http } from "../../utils/http";
import baseURL from "../url";

// 标签对象接口
export interface TagInstance {
  id: number;
  name: string;
  key: string;
  groupId: number;
  score: number;
}

// 标签组接口
export interface TagGroupInstance {
  id: number;
  libraryId: number;
  name: string;
  key: string;
  tags: TagInstance[];
  score: number;
}

// 标签查询响应接口
export interface TagQueryReply {
  tags: TagGroupInstance[];
}

/**
 * @description: 获取知识库标签信息
 * @param {number} libraryId 知识库ID
 * @return {Promise<TagQueryReply>} 标签信息
 */
export function getTagQuery(libraryId: number) {
  return http.get<TagQueryReply, any>(
    `${baseURL.aisaas}/web/${libraryId}/tag/query`
  );
}

// 优秀案例列表查询参数接口
export interface ExcellentCasesQueryParams {
  actionId?: string; // 通话ID，精确匹配
  workerId?: string | number; // 坐席ID
  workerName?: string; // 坐席名称
  orgId?: string | number; // 机构ID
  beginTime?: number; // 查询开始时间，Unix时间戳（秒级）
  endTime?: number; // 查询结束时间，Unix时间戳（秒级）
  minDuration?: number; // 最小通话时长（秒）
  maxDuration?: number; // 最大通话时长（秒）
  pages?: number; // 页码，从1开始
  pageSize?: number; // 每页数量，默认10
  // AI评级相关参数
  aiAppraiseScore?: string; // AI评级分数 (A/B/C/D)
  // 人工评级相关参数
  humanAppraiseScore?: string; // 人工评级分数 (A/B/C/D)
  humanAppraiseResult?: string; // 人工评级结果 (通过/不通过)
  infoUUID?: string; // 线索ID
  // 新增字段
  showPage?: number; // 展示页面,1:首通优秀录音库，2.后续跟进录音库
  audioTags?: string[]; // 录音标签
  // 兼容旧版本参数名称
  callRating?: string; // 通话评级（兼容参数，映射到aiAppraiseScore）
  reinspectionResult?: string; // 复检结果（兼容参数，映射到humanAppraiseResult）
}

// 优秀案例列表项接口（与API文档保持一致）
export interface ExcellentCaseItem {
  id: string; // 案例ID
  workerId?: number; // 坐席ID
  workerName?: string; // 坐席名称
  infoUUID?: string; // 线索ID
  status?: string; // 状态
  actionId?: string; // 通话ID
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
  begin?: string; // 通话开始时间
  end?: string; // 通话结束时间
  aiAppraise?: CaseAppraiseInstance; // AI评级信息
  humanAppraise?: CaseAppraiseInstance; // 人工评级信息
  triggerOrderID?: string; // 触发的成单ID
  userId?: string; // 用户ID
  aiAppraiseStatus?: string; // AI评级状态 (fail/success/process)
  audioURL?: string; // 音频地址
  ph?: string; // 客户手机号
  callTimeLength?: number; // 通话时长（秒）
  lastGroupName?: string; // 最后分组名称
  showPage?: number; // 展示页面,1:首通优秀录音库，2.后续跟进录音库
  audioTags?: string[]; // 录音标签

  // 兼容旧版本字段
  questionCount?: number; // 问题数量（兼容字段）
  addedQuestionCount?: number; // 已添加问题数量（兼容字段）
  callRating?: string; // 通话评级（兼容字段，映射自aiAppraise.score）
  reinspectionResult?: string; // 复检结果（兼容字段，映射自humanAppraise.reason）
  duration?: number; // 通话时长（兼容字段，映射自callTimeLength）
}

// 优秀案例列表响应接口
export interface ExcellentCasesQueryReply {
  list: ExcellentCaseItem[];
  total: string; // 总记录数，API返回string类型
}

// 优秀案例更新接口
export interface ExcellentCaseUpdateParams {
  id: number; // 案例ID
  actionId: string; // 通话ID
  questions?: string[]; // 问题列表
  tags?: number[]; // 标签ID列表
  callRating?: string; // 通话评级
  reinspectionResult?: string; // 复检结果
  reviewComments?: string; // 复核意见
  // 新增字段
  humanAppraise?: {
    score?: string; // 人工评级分数
    reason?: string; // 人工评级原因
    result?: string; // 人工评级结果
    audioTags?: string[]; // 录音标签
    addToFollowUpAudioLib?: boolean; // 是否添加到后续跟进录音库
  };
  followUpRecording?: boolean; // 是否为后续跟进录音
  audioTags?: string[]; // 录音标签（字符串数组）
}

// 对话消息接口
export interface ConversationMessage {
  role: "user" | "assistant"; // 消息角色：用户或助手
  content: string; // 消息内容
  start?: string; // 开始时间戳
  time?: number; // 消息时间
}

// 案例对话文本响应接口
export interface ExcellentCaseTextReply {
  text: {
    id: string;
    actionId: string;
    createdAt: string;
    updatedAt: string;
    texts: RoleTextInstance[];
    status: string;
    infoUUID: string;
    userId: string;
    audioURL: string;
    taskId: string;
  };
}

export interface RoleTextInstance {
  role: "user" | "assistant";
  content: string;
  start?: string;
  time?: number;
}

/**
 * @description: 获取优秀案例列表
 * @param params 查询参数，支持按坐席、时间范围、通话时长、评级等条件筛选
 * @return 优秀案例列表，包含案例数组和总数
 */
export function getExcellentCasesList(params: ExcellentCasesQueryParams) {
  // 处理参数映射和时间戳转换
  const apiParams: any = { ...params };

  // 兼容旧版本参数名称映射
  if (params.callRating && !params.aiAppraiseScore) {
    apiParams.aiAppraiseScore = params.callRating;
    delete apiParams.callRating;
  }

  if (params.reinspectionResult && !params.humanAppraiseResult) {
    apiParams.humanAppraiseResult = params.reinspectionResult;
    delete apiParams.reinspectionResult;
  }

  // 时间戳转换：前端传入毫秒，API需要秒级时间戳
  if (apiParams.beginTime && apiParams.beginTime > 1000000000000) {
    apiParams.beginTime = Math.floor(apiParams.beginTime / 1000);
  }

  if (apiParams.endTime && apiParams.endTime > 1000000000000) {
    apiParams.endTime = Math.floor(apiParams.endTime / 1000);
  }

  return http
    .get<any, ExcellentCasesQueryReply>(
      `${baseURL.robot}/admin/excellentCase`,
      { params: apiParams }
    )
    .then(response => {
      // 为返回数据中的每个案例项添加showPage默认值
      if (
        response &&
        response.data &&
        response.data.list &&
        Array.isArray(response.data.list)
      ) {
        response.data.list = response.data.list.map(item => ({
          ...item,
          showPage: item.showPage ?? 0 // 如果showPage为null/undefined，则设置默认值为0
        }));
      }
      return response; // 返回完整的response对象，保持原有数据结构
    });
}

/**
 * @description: 更新优秀案例
 * @param {ExcellentCaseUpdateParams} params 更新参数
 * @return {Promise<any>} 更新结果
 */
export function updateExcellentCase(params: ExcellentCaseUpdateParams) {
  return http.post(`${baseURL.robot}/admin/excellentCase`, { data: params });
}

/**
 * @description: 获取优秀案例对话文本
 * @param {string} actionId 通话ID
 * @return {Promise<ExcellentCaseTextReply>} 对话文本信息
 */
export function getExcellentCaseText(actionId: string) {
  return http.get<any, ExcellentCaseTextReply>(
    `${baseURL.robot}/admin/excellentCaseText/${actionId}`
  );
}

/** 机器人/人工评级信息 */
export interface CaseAppraiseInstance {
  score?: string; // 评级的分数
  result?: string; // 评级的结果
  createdAt?: string; // 评级时间
  reason?: string; // 原因
  audioTags?: string[]; // 录音标签
  addToFollowUpAudioLib?: boolean; // 是否添加到后续跟进录音库
}

/** 优秀案例详情接口返回类型（info对象） */
export interface ExcellentCaseDetailReply {
  id: string; // 案例ID
  workerId?: number; // 坐席ID
  workerName?: string; // 坐席名称
  infoUUID?: string; // 线索ID
  status?: string; // 状态
  actionId?: string; // 通话ID
  createdAt?: string; // 创建时间
  updatedAt?: string; // 更新时间
  begin?: string; // 通话开始时间
  end?: string; // 通话结束时间
  aiAppraise?: CaseAppraiseInstance; // 机器人评级
  humanAppraise?: CaseAppraiseInstance; // 人工评级
  triggerOrderID?: string; // 触发的成单ID
  userId?: string; // 用户ID
  aiAppraiseStatus?: string; // 机器人评级成功状态 fail success process
  audioURL?: string; // 音频地址
  ph?: string; // ph
  CallTimeLength?: number; // 通话时长
}

/**
 * @description: 查询优秀案例详情
 * @param {string|number} id 案例ID
 * @return {Promise<{ info: ExcellentCaseDetailReply }>} 案例详情
 */
export function getExcellentCaseDetail(id: string | number) {
  return http.get<any, { info: ExcellentCaseDetailReply }>(
    `${baseURL.robot}/admin/excellentCase/${id}`
  );
}

// 重试下载录音请求接口
export interface ExcellentCaseTextRetryRequest {
  actionIds: string[]; // 通话ID列表
}

/**
 * @description: 重试下载录音
 * @param {ExcellentCaseTextRetryRequest} params 重试参数，包含通话ID列表
 * @return {Promise<any>} 重试结果
 */
export function retryExcellentCaseAudio(params: ExcellentCaseTextRetryRequest) {
  return http.post(`${baseURL.robot}/admin/excellentCaseText/retry`, {
    data: params
  });
}

// 设置案例展示页面请求接口
export interface SetShowPageRequest {
  actionId: string; // 通话ID
  showPage: number; // 展示页面,1:首通优秀录音库，2.后续跟进录音库
  audioTags?: string[]; // 录音标签数组
}

/**
 * @description: 设置案例的展示页面
 * @param {SetShowPageRequest} params 设置参数
 * @return {Promise<any>} 设置结果
 */
export function setExcellentCaseShowPage(params: SetShowPageRequest) {
  return http.post(`${baseURL.robot}/admin/excellentCase/setShowPage`, {
    data: params
  });
}
