/**
 * @Date         : 2025-07-14 16:50:00
 * @Description  : AI搜索相关API
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { http } from "/@/utils/http";

// AI搜索消息接口
export interface AiSearchMessage {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: number;
  isConfirmation?: boolean;
  confirmationData?: {
    filters: string;
    timeRange: string;
    intention: string;
  };
}

// AI搜索请求参数
export interface AiSearchRequest {
  message: string;
  sessionId?: string;
  context?: any;
}

// AI搜索响应
export interface AiSearchResponse {
  message: string;
  isConfirmation: boolean;
  confirmationData?: {
    filters: string;
    timeRange: string;
    intention: string;
  };
  sessionId: string;
}

// 筛选条件确认请求
export interface FilterConfirmRequest {
  sessionId: string;
  filters: {
    timeRange?: string;
    intention?: string;
    [key: string]: any;
  };
}

// 筛选结果响应
export interface FilterResultResponse {
  data: any[];
  total: number;
  filters: any;
}

/**
 * 发送AI搜索消息
 * @param data 搜索请求参数
 * @returns Promise<AiSearchResponse>
 */
export const sendAiSearchMessage = (data: AiSearchRequest) => {
  return http.request<AiSearchResponse>("post", "/api/ai-search/message", {
    data
  });
};

/**
 * 重新解析用户消息
 * @param data 重新解析请求参数
 * @returns Promise<AiSearchResponse>
 */
export const reparseAiMessage = (data: AiSearchRequest) => {
  return http.request<AiSearchResponse>("post", "/api/ai-search/reparse", {
    data
  });
};

/**
 * 确认筛选条件并获取结果
 * @param data 筛选条件确认请求
 * @returns Promise<FilterResultResponse>
 */
export const confirmFiltersAndSearch = (data: FilterConfirmRequest) => {
  return http.request<FilterResultResponse>("post", "/api/ai-search/confirm", {
    data
  });
};

/**
 * 获取AI搜索会话历史
 * @param sessionId 会话ID
 * @returns Promise<AiSearchMessage[]>
 */
export const getAiSearchHistory = (sessionId: string) => {
  return http.request<AiSearchMessage[]>("get", `/api/ai-search/history/${sessionId}`);
};

/**
 * 清除AI搜索会话
 * @param sessionId 会话ID
 * @returns Promise<boolean>
 */
export const clearAiSearchSession = (sessionId: string) => {
  return http.request<boolean>("delete", `/api/ai-search/session/${sessionId}`);
};

// 模拟API响应（开发阶段使用）
export const mockAiSearchResponse = (message: string): Promise<AiSearchResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟不同类型的响应
      if (message.includes('查询') || message.includes('搜索')) {
        resolve({
          message: `请确认您的筛选条件是否正确：\n最近一次看课时间：2025-05-26 0:00:00至2025-05-26 23:59:59\n意向度：意向`,
          isConfirmation: true,
          confirmationData: {
            filters: '最近一次看课时间：2025-05-26 0:00:00至2025-05-26 23:59:59',
            timeRange: '2025-05-26 0:00:00至2025-05-26 23:59:59',
            intention: '意向'
          },
          sessionId: 'session_' + Date.now()
        });
      } else {
        resolve({
          message: '我理解您的需求，正在为您分析...',
          isConfirmation: false,
          sessionId: 'session_' + Date.now()
        });
      }
    }, 1000);
  });
};

// 模拟筛选结果API
export const mockFilterResults = (): Promise<FilterResultResponse> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: [
          {
            id: 1,
            name: '张三',
            phone: '13800138001',
            intention: '意向',
            lastViewTime: '2025-05-26 10:30:00'
          },
          {
            id: 2,
            name: '李四',
            phone: '13800138002',
            intention: '意向',
            lastViewTime: '2025-05-26 14:20:00'
          }
        ],
        total: 2,
        filters: {
          timeRange: '2025-05-26 0:00:00至2025-05-26 23:59:59',
          intention: '意向'
        }
      });
    }, 800);
  });
};
