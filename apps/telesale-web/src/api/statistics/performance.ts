/*
 * @Date         : 2025-07-17 16:00:00
 * @Description  : 业绩统计相关API
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { http } from "../../utils/http";
import baseURL from "../url";

// 业绩排名请求参数
export interface PerformanceRankingRequest {
  month: string; // 格式: YYYY-MM
}

// 业绩排名响应数据
export interface PerformanceRankingResponse {
  ranking: number; // 我的排名
  revenue: number; // 本月业绩
}

// 个人目标请求参数
export interface PersonalTargetRequest {
  month: string; // 格式: YYYY-MM
}

// 个人目标响应数据
export interface PersonalTargetResponse {
  target: number; // 本月目标
}

// 我的业绩请求参数
export interface MyPerformanceRequest {
  timeType: "week" | "month" | "year";
  time: string; // 根据timeType格式不同: YYYY-[W]WW / YYYY-MM / YYYY
}

// 我的业绩响应数据
export interface MyPerformanceResponse {
  date: string;
  amount: number;
}

// 业绩分类请求参数
export interface PerformanceClassificationRequest {
  timeType: "week" | "month" | "year";
  time: string;
}

// 业绩分类响应数据
export interface PerformanceClassificationResponse {
  name: string;
  value: number;
}

// 客户来源请求参数
export interface CustomerSourceRequest {
  timeType: "week" | "month" | "year";
  time: string;
}

// 客户来源响应数据
export interface CustomerSourceResponse {
  name: string;
  value: number;
}

/**
 * @description: 获取个人排行榜-营收数据
 * @param {PerformanceRankingRequest} data
 */
export const getPerformanceRanking = (data: PerformanceRankingRequest) => {
  return http.request<{ data: PerformanceRankingResponse }>(
    "post",
    `${baseURL.api}/statistics/performance/ranking`,
    {
      data
    }
  );
};

/**
 * @description: 获取个人目标
 * @param {PersonalTargetRequest} data
 */
export const getPersonalTarget = (data: PersonalTargetRequest) => {
  return http.request<{ data: PersonalTargetResponse }>(
    "post",
    `${baseURL.api}/statistics/performance/target`,
    {
      data
    }
  );
};

/**
 * @description: 获取我的业绩数据
 * @param {MyPerformanceRequest} data
 */
export const getMyPerformance = (data: MyPerformanceRequest) => {
  return http.request<{ data: MyPerformanceResponse[] }>(
    "post",
    `${baseURL.api}/statistics/performance/my-performance`,
    {
      data
    }
  );
};

/**
 * @description: 获取业绩分类数据
 * @param {PerformanceClassificationRequest} data
 */
export const getPerformanceClassification = (data: PerformanceClassificationRequest) => {
  return http.request<{ data: PerformanceClassificationResponse[] }>(
    "post",
    `${baseURL.api}/statistics/performance/classification`,
    {
      data
    }
  );
};

/**
 * @description: 获取客户来源数据
 * @param {CustomerSourceRequest} data
 */
export const getCustomerSource = (data: CustomerSourceRequest) => {
  return http.request<{ data: CustomerSourceResponse[] }>(
    "post",
    `${baseURL.api}/statistics/performance/customer-source`,
    {
      data
    }
  );
};

/**
 * @description: 获取已成交客户来源数据
 * @param {CustomerSourceRequest} data
 */
export const getDealCustomerSource = (data: CustomerSourceRequest) => {
  return http.request<{ data: CustomerSourceResponse[] }>(
    "post",
    `${baseURL.api}/statistics/performance/deal-customer-source`,
    {
      data
    }
  );
};
