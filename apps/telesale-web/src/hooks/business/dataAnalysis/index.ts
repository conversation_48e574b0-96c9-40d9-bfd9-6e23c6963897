/*
 * @Date         : 2025-03-28 17:05:17
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import { findMember } from "/@/api/agent";

export const useSelectGroup = () => {
  const { orgData, userMsg } = storeToRefs(useUserStore());
  const orgId = ref();
  const groupList = ref([]);
  const workerList = ref([]);

  const init = () => {
    console.log(orgData.value);
    const list = findNodes(orgData.value, 3);
    if (list.length === 1) {
      orgId.value = list[0].value;
    }
    groupList.value = list;
  };

  const getWorkerList = async () => {
    const res: any = await findMember({ id: orgId.value });
    console.log(res);
    workerList.value = res.data.list?.map(item => {
      return {
        label: item.name,
        value: item.id
      };
    });
  };

  init();

  watch(
    () => orgId.value,
    n => {
      if (n) {
        getWorkerList();
      }
    }
  );

  return {
    orgId,
    groupList,
    workerList,
    leafNode: userMsg.value.leafNode
  };
};

// 递归找出数组中level为2的所有节点
export const findNodes = (arr, level) => {
  let result = [];
  arr.forEach(item => {
    if (item.level === level) {
      const list =
        item.children?.map(child => {
          return {
            label: child.name,
            value: child.id
          };
        }) || [];
      result.push(...list);
    }
    if (item.children && item.children.length > 0) {
      result = result.concat(findNodes(item.children, level));
    }
  });
  return result;
};
