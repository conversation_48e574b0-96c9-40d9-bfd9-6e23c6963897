/*
 * @Date         : 2024-11-29 12:09:23
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */
import { useLocalStorage } from "@vueuse/core";
import {
  createLocalViewApi,
  getViewApi,
  getViewListApi,
  saveOrResetViewApi,
  ViewData,
  ViewInfo,
  ViewList
} from "/@/api/common/view";

export const useView = (key: "客户池" | "已成交") => {
  const loading = ref(false);
  const tableLoading = ref(false);
  const currentId = ref<number>(undefined);
  const hasPermission = ref(false);
  const currentViewData = ref<ViewInfo>();
  const dataList = ref<ViewList[]>([]);
  const newTableRef = ref();
  const viewData = ref<ViewData>({
    search: {},
    tableSelect: {},
    tableSort: {},
    tableColumns: []
  });
  const localTableColumns = ref();
  provide("viewData", viewData);

  const initTable = (e: ViewData) => {
    viewData.value = e;
    newTableRef.value?.initTable();
  };

  const getView = () => {
    loading.value = true;
    getViewApi({ id: currentId.value })
      .then(res => {
        currentViewData.value = res.data;
        const data = JSON.parse(res.data.viewData);
        getLocalTableColumns();
        if (!localTableColumns.value) {
          setLocalTableColumns(data.tableColumns);
        } else {
          data.tableColumns = localTableColumns.value;
        }
        initTable(data);
        hasPermission.value = dataList.value.find(
          item => item.id === currentId.value
        )?.hasPermission;
      })
      .catch(err => {
        if (err.response.data === "视图已被删除") {
          getData();
        }
      })
      .finally(() => {
        setTimeout(() => {
          loading.value = false;
        }, 200);
      });
  };

  const setViewData = (val: Partial<ViewData>) => {
    viewData.value = { ...viewData.value, ...val };
    if (!currentId.value) {
      newTableRef.value?.getList();
      return;
    }
    loading.value = true;
    createLocalViewApi({
      parentId: currentId.value,
      viewData: JSON.stringify(viewData.value)
    })
      .then(() => {
        setLocalTableColumns(viewData.value.tableColumns);
        getView();
      })
      .catch(err => {
        if (err.response.data === "视图已被删除") {
          getData();
        }

        loading.value = false;
      });
  };

  provide("setViewData", setViewData);

  const save = (type: "save" | "reset") => {
    loading.value = true;
    saveOrResetViewApi({
      id: currentId.value,
      action: type
    })
      .then(res => {
        currentViewData.value = res.data;
        const data = JSON.parse(res.data.viewData);
        data.tableColumns = localTableColumns.value;
        initTable(data);
        hasPermission.value = dataList.value.find(
          item => item.id === currentId.value
        )?.hasPermission;
      })
      .catch(err => {
        if (err.response.data === "视图已被删除") {
          getData();
        }
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const getData = (isSet: boolean = true) => {
    loading.value = true;
    getViewListApi({ belong: key })
      .then(res => {
        dataList.value = res.data.viewList;
        if (dataList.value.length > 0 && isSet) {
          if (currentId.value === dataList.value[0].id) {
            loading.value = false;
          } else {
            currentId.value = dataList.value[0].id;
          }
        } else {
          loading.value = false;
        }
      })
      .catch(() => {
        loading.value = false;
      });
  };

  getData();

  const setLoading = (val: boolean) => {
    tableLoading.value = val;
  };

  const getLocalTableColumns = () => {
    const id =
      currentViewData.value.parentId === 0
        ? currentId.value
        : currentViewData.value.parentId;
    const data = localStorage.getItem(`localTableColumns-${key}-${id}`);

    localTableColumns.value = data ? JSON.parse(data) : undefined;
  };

  const setLocalTableColumns = data => {
    const id =
      currentViewData.value.parentId === 0
        ? currentId.value
        : currentViewData.value.parentId;
    if (data) {
      localStorage.setItem(
        `localTableColumns-${key}-${id}`,
        JSON.stringify(data)
      );
    }
    localTableColumns.value = data;
  };

  watch(
    () => currentId.value,
    n => {
      if (n) {
        getView();
      }
    }
  );
  return {
    loading,
    tableLoading,
    currentId,
    hasPermission,
    currentViewData,
    dataList,
    newTableRef,
    viewData,
    setViewData,
    initTable,
    getData,
    setLoading,
    save
  };
};
