import { toRouteType } from "./types";
import NProgress from "/@/utils/progress";
import { buildHierarchyTree } from "/@/utils/tree";
import {
  Router,
  createRouter,
  RouteRecordRaw,
  createWebHistory
} from "vue-router";
import { ElMessage } from "element-plus";
import {
  // getHistoryMode,
  handleAliveRoute,
  formatTwoStageRoutes,
  formatFlatteningRoutes
} from "./utils";

import homeRouter from "./modules/home";
import dataPoolRouter from "./modules/dataPool";
import customerRouter from "./modules/customer";
import orderRouter from "./modules/order";
import statisticsRouter from "./modules/statistics";
import activeRouter from "./modules/active";
import integralMallRouter from "./modules/integralMall";
import salarySystemRouter from "./modules/salarySystem";
import systemRouter from "./modules/system";
import agentRouter from "./modules/agent";
import dailyRouter from "./modules/daily";
import testRouter from "./modules/test";
import setting from "./modules/setting";
import warmRouter from "./modules/warm";
import errorRouter from "./modules/error";
import remainingRouter from "./modules/remaining";
import salesmanRouter from "./modules/salesman";
import dataAnalysisRouter from "./modules/dataAnalysis";
// import aladdinRouter from "./modules/aladdin";
import { useUserStoreHook } from "/@/store/modules/user";
import { registToken } from "/@/api/user";
import findAgentMath from "/@/utils/asyn/findAgent";
import findAllAgentMath from "/@/utils/asyn/findAllAgent";
import getPersonMsgMath from "/@/utils/asyn/getPersonMsg";
import findReferOrgMath from "/@/utils/asyn/findReferOrg";
import loginAllMsgMath from "/@/utils/asyn/loginAllMsg";
import { getTenantMsg } from "/@/api/user";
import menuDataMath from "/@/utils/handle/menuDataMath";
import tenantExit from "/@/utils/handle/tenantExit";
import AISupportRouter from "./modules/AISupport";
import aiQualityInspectionRouter from "./modules/aiQualityInspection";

const pureUser = useUserStoreHook();

// 原始静态路由（未做任何处理）
const routes = [
  homeRouter,
  dataPoolRouter,
  customerRouter,
  orderRouter,
  statisticsRouter,
  activeRouter,
  integralMallRouter,
  salarySystemRouter,
  systemRouter,
  agentRouter,
  dailyRouter,
  testRouter,
  warmRouter,
  setting,
  salesmanRouter,
  // aladdinRouter,
  errorRouter,
  AISupportRouter,
  dataAnalysisRouter,
  aiQualityInspectionRouter
];

// 导出处理后的静态路由（三级及以上的路由全部拍成二级）
export const constantRoutes: Array<RouteRecordRaw> = formatTwoStageRoutes(
  formatFlatteningRoutes(buildHierarchyTree(routes))
);

// 创建路由实例
export const router: Router = createRouter({
  history: createWebHistory("WH_CRM_v2"),
  routes: constantRoutes.concat(...remainingRouter),
  strict: true,
  scrollBehavior(to, from, savedPosition) {
    return new Promise(resolve => {
      if (savedPosition) {
        return savedPosition;
      } else {
        if (from.meta.saveSrollTop) {
          const top: number =
            document.documentElement.scrollTop || document.body.scrollTop;
          resolve({ left: 0, top });
        }
      }
    });
  }
});

//微前端登录后需要处理的部分
function registTokenMath(next) {
  localStorage.setItem(
    "whcrmShadowAuthorization",
    localStorage.getItem("ShadowAuthorization")
  );
  registToken()
    .then(() => {
      getTenantMsgMath(next);
    })
    .catch(() => {
      ElMessage.error("token注入失败！");
      localStorage.removeItem("whcrmShadowAuthorization");
      pureUser.signOut();
    });
}

//获取登录信息
function getTenantMsgMath(next) {
  getTenantMsg()
    .then(({ data }: { data: any }) => {
      pureUser.setUser({
        username: data.username,
        name: data.name,
        mail: data.mail
      });
      pureUser.setAuthorizationMap(data.permissions);
      menuDataMath();
      pureUser.setTenantBelongSite({
        id: data.siteId,
        name: data.siteName
      });
      getMustMsg(next);
    })
    .catch(({ response }) => {
      if (response.status !== 401 && response.status !== 403) {
        ElMessage.error("获取用户信息失败，请重新登录");
        const timer = setTimeout(() => {
          clearTimeout(timer);
          pureUser.isPageWay === "2"
            ? tenantExit()
            : useUserStoreHook().signOut();
          localStorage.removeItem("whcrmShadowAuthorization");
        }, 2000);
      }
    });
}

//获取通用信息
async function getMustMsg(next) {
  const isNoAgentList = !pureUser.agentList.length;
  const isNoAllAgentObj = !pureUser.allAgentObj;
  const isNoUserMsg =
    !pureUser.userMsg || JSON.stringify(pureUser.userMsg) === "{}";
  const isNoOrgData = !pureUser.orgData.length;
  if (isNoAgentList && isNoAllAgentObj && isNoUserMsg && isNoOrgData) {
    await loginAllMsgMath();
  } else {
    isNoAgentList && pureUser.setAgentList(await findAgentMath());
    isNoAllAgentObj && pureUser.setAllAgentObj(await findAllAgentMath());
    isNoUserMsg && pureUser.setUserMsg(await getPersonMsgMath());
    isNoOrgData && pureUser.setOrgData(await findReferOrgMath());
  }
  next();
}

router.beforeEach((to: toRouteType, _from, next) => {
  if (to.meta?.keepAlive) {
    const newMatched = to.matched;
    handleAliveRoute(newMatched, "add");
    // 页面整体刷新和点击标签页刷新
    if (_from.name === undefined || _from.name === "redirect") {
      handleAliveRoute(newMatched);
    }
  }
  NProgress.start();
  const inPageWay = localStorage.getItem("whcrm_inPageWay");

  // @ts-ignore
  if (window._hmt && to.path) {
    // @ts-ignore
    window._hmt.push(["_trackPageview", "/WH_CRM_v2" + to.fullPath]);
  }

  if (to.path !== "/login") {
    if (pureUser.user.mail) {
      getMustMsg(next);
    } else {
      inPageWay !== "2" ? registTokenMath(next) : getTenantMsgMath(next);
    }
  } else {
    next();
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
