/*
 * @Date         : 2025-03-28 16:53:46
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

const Layout = () => import("/@/layout/index.vue");

const dataAnalysisRouter = {
  path: "/dataAnalysis",
  component: Layout,
  redirect: "noRedirect",
  meta: {
    title: "数据分析"
  },
  children: [
    {
      path: "/dataAnalysis/group/index",
      name: "GroupDataAnalysis",
      component: () => import("/@/views/dataAnalysis/group/index.vue"),
      meta: {
        title: "小组数据分析"
      }
    }
  ]
};

export default dataAnalysisRouter;
