<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useAppStoreHook } from "/@/store/modules/app";
import { useTable } from "/@/hooks/useTable";
import { getTransferGroupReferral } from "/@/api/statistics";
import { getPlatformListApi } from "@telesale/server/src/api/active/transfer";
import paramsHandle from "/@/utils/handle/paramsHandle";
import originDate from "/@/utils/handle/originDate";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import FormOrgAgent from "/@/components/FormOrgAgent/index.vue";
import { listHeader } from "../utils/groupList";
import { totalList, sumInit } from "../utils/sumList";

let device = useAppStoreHook().device;

const sum = ref({ ...sumInit });
const platformList = ref([]);
const followIdtRef = ref();
const workerIdRef = ref();
const formRef = ref<FormInstance>();
const tableRefs = ref();

// 使用useTable管理表格数据
const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: getTransferGroupReferral,
    immediate: false,
    beforeRequest: params => {
      return paramsHandle(params, {
        newTime: true,
        zero: ["orgId", "platformId"]
      });
    },
    dataCallback: res => {
      res.data.collect.convRate = (res.data.collect.convRate * 100).toFixed(2);
      sum.value = res.data.collect;
      console.log("sum.value", res.data.collect);
    }
  });

// 表单重置
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  followIdtRef.value?.agentListReset();
  workerIdRef.value?.agentListReset();
  clearSort();
  // 重置searchForm到初始状态
  searchForm.value = {
    workerId: undefined,
    orgId: undefined,
    platformId: "",
    followId: undefined,
    followOrgId: undefined,
    time: originDate()
  };
  onSearch();
};

// 排序重置
function clearSort() {
  tableRefs.value?.clearSort();
}

// 排序变化处理
const sortChange = column => {
  if (column.prop && column.order) {
    searchForm.value.sortField = column.prop;

    searchForm.value.sortAscending = column.order
      ? column.order.slice(0, -6)
      : "";
  } else {
    searchForm.value.sortField = undefined;
    searchForm.value.sortAscending = undefined;
  }
  onSearch();
};

// 获取平台列表
function getPlatformList() {
  getPlatformListApi()
    .then(({ data }) => {
      platformList.value = data.list || [];
    })
    .catch(() => {
      platformList.value = [];
    });
}

onMounted(() => {
  searchForm.value.time = originDate();
  handlerQuery();
  getPlatformList();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-form ref="formRef" :inline="true" :model="searchForm" class="clearfix">
      <FormOrgAgent
        ref="followIdtRef"
        workerId="followId"
        orgId="followOrgId"
        agent-list-key="allAgentList"
        isAllOrg
        v-model:form="searchForm"
        showOrg
        placeholderOrg="请选择介绍人小组"
        placeholderWorker="请选择介绍人"
      />
      <FormOrgAgent
        ref="workerIdRef"
        workerId="workerId"
        agent-list-key="allAgentList"
        isAllOrg
        v-model:form="searchForm"
        showOrg
        placeholderOrg="请选择归属坐席小组"
        placeholderWorker="请选择归属坐席"
      />
      <el-form-item prop="platformId">
        <el-select
          v-model="searchForm.platformId"
          placeholder="请选择平台"
          clearable
        >
          <el-option
            v-for="item in platformList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="time">
        <el-date-picker
          v-model="searchForm.time"
          type="daterange"
          value-format="x"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <div class="g-list-box">
      <div v-for="(item, i) in totalList" :key="i">
        <span class="title-name">{{ item.label }}：</span>
        <span class="text-name">{{ sum[item.key] }}{{ item.addStr }}</span>
      </div>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="device !== 'mobile'"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :sort-change="sortChange"
      />
      <template v-else>
        <ReCardList
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :isCardBox="false"
        />
      </template>
    </div>
    <div class="mt-10px">
      <Pagination />
    </div>
  </div>
</template>
