<!--
 * @Date         : 2025-07-17 16:00:00
 * @Description  : 业绩统计页面
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts" name="PerformanceStatistics">
import { ref, onMounted, computed, watch } from "vue";
import dayjs from "dayjs";
import PerformanceRanking from "./components/PerformanceRanking.vue";
import MyPerformance from "./components/MyPerformance.vue";
import CustomerSource from "./components/CustomerSource.vue";
// import { getPerformanceRanking, getPersonalTarget } from "/@/api/statistics/performance";

// 当前选择的月份（业绩排名用）
const selectedMonth = ref(dayjs().format("YYYY-MM"));

// 业绩排名数据
const rankingData = ref({
  myRanking: 0,
  monthlyPerformance: 0,
  monthlyTarget: 0,
  progress: 0
});

// 获取业绩排名数据
const getRankingData = async () => {
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));

    rankingData.value = {
      myRanking: 35,
      monthlyPerformance: 30000,
      monthlyTarget: 60000,
      progress: 50
    };
  } catch (error) {
    console.error("获取业绩数据失败:", error);
  }
};

// 监听月份变化
watch(selectedMonth, () => {
  getRankingData();
});

// 页面初始化
onMounted(() => {
  getRankingData();
});

// 月份选择器的禁用日期函数
const disableFutureMonths = (time: Date) => {
  return time.getTime() > Date.now();
};
</script>

<template>
  <div class="g-margin-20">
    <!-- 业绩排名卡片 -->
    <PerformanceRanking
      v-model:selectedMonth="selectedMonth"
      :rankingData="rankingData"
      :disableFutureMonths="disableFutureMonths"
    />

    <!-- 我的业绩和客户来源 -->
    <el-row :gutter="20" class="mt-20px">
      <el-col :span="12">
        <MyPerformance />
      </el-col>
      <el-col :span="12">
        <CustomerSource />
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.g-margin-20 {
  margin: 20px;
}

.mt-20px {
  margin-top: 20px;
}
</style>
