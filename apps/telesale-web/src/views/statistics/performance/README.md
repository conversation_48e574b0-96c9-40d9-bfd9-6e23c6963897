# 业绩统计页面

## 🎯 功能概述

业绩统计页面提供完整的个人业绩分析功能，包括业绩排名、业绩趋势、业绩分类和客户来源分析。

## 📊 页面结构

### 1. 业绩排名卡片 (PerformanceRanking.vue)

**功能特性：**
- 默认展示当月业绩数据
- 月份选择器，仅可选择当月及之前的月份
- 根据本月目标展示不同的进度条和激励文案

**数据来源：**
- 我的业绩排名：个人排行榜-营收数据
- 本月业绩：个人排行榜-营收数据
- 本月目标：个人目标设置

**进度文案规则：**
- 无目标：显示激励文案，不显示进度条
- 25%：破冰启航，加速狂飙！
- 50%：势如破竹，乘胜追击！
- 75%：雷霆之势，碾碎阻碍！
- 90%：破晓时刻，全力绝杀！
- 100%：巅峰之上，再造传奇！

### 2. 我的业绩 (MyPerformance.vue)

**功能特性：**
- 支持周/月/年时间选择
- 柱状图展示业绩趋势
- 饼图展示业绩分类

**时间颗粒度：**
- 周/月选择：按天展示数据
- 年选择：按月展示数据

**业绩分类：**
- 展示订单类型占比
- 前五名显示具体类型
- 其余归为"其他所有类型"

### 3. 我的客户来源 (CustomerSource.vue)

**功能特性：**
- 支持周/月/年时间选择
- 柱状图展示客户来源分布
- 饼图展示已成交客户来源占比

**数据统计规则：**
- 客户来源：统计一级分类，不展开二级分类
- 已成交客户：在时间范围内有成交订单的客户
- 占比前五显示具体类型，其余归为"其他所有类型"

## 🛠️ 技术实现

### 组件架构

```
performance/
├── index.vue                    # 主页面
├── components/
│   ├── PerformanceRanking.vue  # 业绩排名组件
│   ├── MyPerformance.vue       # 我的业绩组件
│   └── CustomerSource.vue      # 客户来源组件
└── README.md                   # 说明文档
```

### 技术栈

- **Vue 3 + TypeScript**: 组件开发
- **Element Plus**: UI组件库
- **ECharts**: 图表组件
- **Day.js**: 时间处理
- **SCSS**: 样式预处理

### 图表配置

**柱状图配置：**
```typescript
const chartOption: ECOption = {
  tooltip: { trigger: "axis" },
  xAxis: { type: "category", data: xAxisData },
  yAxis: { type: "value" },
  series: [{
    type: "bar",
    data: seriesData,
    itemStyle: { color: "#409EFF" }
  }]
};
```

**饼图配置：**
```typescript
const pieOption: ECOption = {
  tooltip: { trigger: "item" },
  legend: { orient: "vertical", right: "10%" },
  series: [{
    type: "pie",
    radius: ["40%", "70%"],
    data: pieData
  }]
};
```

## 📱 响应式设计

- **桌面端**: 网格布局，图表并排显示
- **移动端**: 垂直布局，图表堆叠显示
- **时间控制**: 自适应布局，小屏幕下垂直排列

## 🔌 API接口

### 业绩排名相关

```typescript
// 获取个人排行榜-营收数据
getPerformanceRanking(data: { month: string })

// 获取个人目标
getPersonalTarget(data: { month: string })
```

### 业绩数据相关

```typescript
// 获取我的业绩数据
getMyPerformance(data: { 
  timeType: "week" | "month" | "year",
  time: string 
})

// 获取业绩分类数据
getPerformanceClassification(data: { 
  timeType: "week" | "month" | "year",
  time: string 
})
```

### 客户来源相关

```typescript
// 获取客户来源数据
getCustomerSource(data: { 
  timeType: "week" | "month" | "year",
  time: string 
})

// 获取已成交客户来源数据
getDealCustomerSource(data: { 
  timeType: "week" | "month" | "year",
  time: string 
})
```

## 🎨 样式规范

### 颜色主题

- **主色调**: #409EFF (蓝色)
- **成功色**: #67C23A (绿色)
- **警告色**: #E6A23C (橙色)
- **危险色**: #F56C6C (红色)
- **信息色**: #909399 (灰色)

### 布局规范

- **卡片间距**: 20px
- **组件内边距**: 20px
- **图表高度**: 300px
- **响应式断点**: 768px

## 🚀 使用方式

1. **页面引入**：
```vue
<template>
  <div class="g-margin-20">
    <PerformanceRanking />
    <el-row :gutter="20">
      <el-col :span="12">
        <MyPerformance />
      </el-col>
      <el-col :span="12">
        <CustomerSource />
      </el-col>
    </el-row>
  </div>
</template>
```

2. **数据获取**：
```typescript
// 监听时间变化，自动刷新数据
watch([timeType, selectedTime], () => {
  getData();
});
```

3. **图表渲染**：
```vue
<Echarts :option="chartOption" height="300" />
```

## 📈 数据流

```
用户选择时间 → API请求 → 数据处理 → 图表渲染 → 用户交互
     ↓              ↓          ↓          ↓          ↓
  时间验证      模拟数据    格式转换    ECharts    工具提示
```

这个业绩统计页面提供了完整的个人业绩分析功能，支持多维度的数据展示和交互操作。
