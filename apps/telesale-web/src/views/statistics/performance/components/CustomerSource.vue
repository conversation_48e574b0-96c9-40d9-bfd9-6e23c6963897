<!--
 * @Date         : 2025-07-17 16:00:00
 * @Description  : 我的客户来源组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import dayjs from "dayjs";
import Echarts from "/@/components/Echarts/index.vue";
import { ECOption } from "/@/components/Echarts/config/index";

// 时间选择类型
type TimeType = "week" | "month" | "year";

// 当前选择的时间类型
const timeType = ref<TimeType>("month");

// 当前选择的时间范围
const selectedTime = ref(dayjs().format("YYYY-MM"));

// 加载状态
const loading = ref(false);

// 客户来源数据
const customerSourceData = ref<any[]>([]);

// 已成交客户来源数据
const dealCustomerSourceData = ref<any[]>([]);

// 时间选择器配置
const timePickerConfig = computed(() => {
  switch (timeType.value) {
    case "week":
      return {
        type: "week",
        format: "YYYY-[W]WW",
        valueFormat: "YYYY-[W]WW",
        placeholder: "选择周"
      };
    case "month":
      return {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        placeholder: "选择月份"
      };
    case "year":
      return {
        type: "year",
        format: "YYYY",
        valueFormat: "YYYY",
        placeholder: "选择年份"
      };
    default:
      return {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        placeholder: "选择月份"
      };
  }
});

// 禁用未来时间
const disableFutureTime = (time: Date) => {
  return time.getTime() > Date.now();
};

// 客户来源柱状图配置
const customerSourceChartOption = computed<ECOption>(() => {
  const xAxisData = customerSourceData.value.map(item => item.name);
  const seriesData = customerSourceData.value.map(item => item.value);
  
  return {
    tooltip: {
      trigger: "axis",
      formatter: "{b}<br/>{a}: {c}人"
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLabel: {
        rotate: 45,
        interval: 0
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value}人"
      }
    },
    series: [
      {
        name: "客户数量",
        type: "bar",
        data: seriesData,
        itemStyle: {
          color: "#67C23A"
        },
        barWidth: "60%"
      }
    ],
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true
    }
  };
});

// 已成交客户来源饼图配置
const dealCustomerSourceChartOption = computed<ECOption>(() => {
  const colors = ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#909399", "#C0C4CC"];
  
  return {
    tooltip: {
      trigger: "item",
      formatter: "{a}<br/>{b}: {c}人 ({d}%)"
    },
    legend: {
      orient: "vertical",
      right: "10%",
      top: "center",
      itemWidth: 12,
      itemHeight: 12
    },
    series: [
      {
        name: "已成交客户来源",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["40%", "50%"],
        data: dealCustomerSourceData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: false
        }
      }
    ]
  };
});

// 获取客户来源数据
const getCustomerSourceData = async () => {
  loading.value = true;
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 客户来源数据（一级分类）
    customerSourceData.value = [
      { name: "线上推广", value: 362 },
      { name: "人工推荐", value: 208 },
      { name: "主动咨询", value: 92 },
      { name: "老客户介绍", value: 16 },
      { name: "线下活动", value: 434 },
      { name: "其他渠道", value: 105 },
      { name: "合作伙伴", value: 1098 },
      { name: "社交媒体", value: 8 },
      { name: "电话营销", value: 15 },
      { name: "邮件营销", value: 366 },
      { name: "成交客户介绍", value: 9 },
      { name: "展会获客", value: 59 }
    ];
    
    // 已成交客户来源数据（占比前五 + 其他）
    dealCustomerSourceData.value = [
      { name: "线上推广", value: 45 },
      { name: "合作伙伴", value: 28 },
      { name: "人工推荐", value: 15 },
      { name: "线下活动", value: 8 },
      { name: "主动咨询", value: 3 },
      { name: "其他所有类型", value: 1 }
    ];
  } catch (error) {
    console.error("获取客户来源数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 时间类型变化
const handleTimeTypeChange = (type: TimeType) => {
  timeType.value = type;
  // 重置时间选择
  switch (type) {
    case "week":
      selectedTime.value = dayjs().format("YYYY-[W]WW");
      break;
    case "month":
      selectedTime.value = dayjs().format("YYYY-MM");
      break;
    case "year":
      selectedTime.value = dayjs().format("YYYY");
      break;
  }
};

// 监听时间变化
watch([timeType, selectedTime], () => {
  getCustomerSourceData();
});

// 页面初始化
onMounted(() => {
  getCustomerSourceData();
});
</script>

<template>
  <el-card v-loading="loading" class="customer-source-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">我的客户来源</span>
        <div class="time-controls">
          <!-- 时间类型选择 -->
          <el-radio-group 
            :model-value="timeType" 
            size="small"
            @update:model-value="handleTimeTypeChange"
          >
            <el-radio-button label="week">周</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>
          
          <!-- 时间选择器 -->
          <el-date-picker
            v-model="selectedTime"
            :type="timePickerConfig.type"
            :format="timePickerConfig.format"
            :value-format="timePickerConfig.valueFormat"
            :placeholder="timePickerConfig.placeholder"
            :disabled-date="disableFutureTime"
            :clearable="false"
            size="small"
            style="width: 140px; margin-left: 10px;"
          />
        </div>
      </div>
    </template>
    
    <!-- 客户来源图表 -->
    <div class="chart-container">
      <div class="chart-section">
        <h4 class="chart-title">我的客户来源</h4>
        <Echarts :option="customerSourceChartOption" height="300" />
      </div>
      
      <div class="chart-section">
        <h4 class="chart-title">已成交客户来源</h4>
        <Echarts :option="dealCustomerSourceChartOption" height="300" />
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped>
.customer-source-card {
  height: 500px;
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
    
    .time-controls {
      display: flex;
      align-items: center;
    }
  }
  
  .chart-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: calc(100% - 60px);
    
    .chart-section {
      .chart-title {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .customer-source-card {
    .card-header {
      flex-direction: column;
      gap: 10px;
      align-items: stretch;
    }
    
    .chart-container {
      grid-template-columns: 1fr;
      gap: 10px;
    }
  }
}
</style>
