<!--
 * @Date         : 2025-07-17 16:00:00
 * @Description  : 我的业绩组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import dayjs from "dayjs";
import Echarts from "/@/components/Echarts/index.vue";
import { ECOption } from "/@/components/Echarts/config/index";
// import { getMyPerformance, getPerformanceClassification } from "/@/api/statistics/performance";

// 时间选择类型
type TimeType = "week" | "month" | "year";

// 当前选择的时间类型
const timeType = ref<TimeType>("month");

// 当前选择的时间范围
const selectedTime = ref(dayjs().format("YYYY-MM"));

// 加载状态
const loading = ref(false);

// 业绩数据
const performanceData = ref<any[]>([]);

// 业绩分类数据
const classificationData = ref<any[]>([]);

// 时间选择器配置
const timePickerConfig = computed(() => {
  switch (timeType.value) {
    case "week":
      return {
        type: "week",
        format: "YYYY-[W]WW",
        valueFormat: "YYYY-[W]WW",
        placeholder: "选择周"
      };
    case "month":
      return {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        placeholder: "选择月份"
      };
    case "year":
      return {
        type: "year",
        format: "YYYY",
        valueFormat: "YYYY",
        placeholder: "选择年份"
      };
    default:
      return {
        type: "month",
        format: "YYYY-MM",
        valueFormat: "YYYY-MM",
        placeholder: "选择月份"
      };
  }
});

// 禁用未来时间
const disableFutureTime = (time: Date) => {
  return time.getTime() > Date.now();
};

// 业绩柱状图配置
const performanceChartOption = computed<ECOption>(() => {
  const xAxisData = performanceData.value.map(item => item.date);
  const seriesData = performanceData.value.map(item => item.amount);

  return {
    tooltip: {
      trigger: "axis",
      formatter: "{b}<br/>{a}: ¥{c}"
    },
    xAxis: {
      type: "category",
      data: xAxisData,
      axisLabel: {
        rotate: timeType.value === "month" ? 45 : 0
      }
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "¥{value}"
      }
    },
    series: [
      {
        name: "业绩",
        type: "bar",
        data: seriesData,
        itemStyle: {
          color: "#409EFF"
        },
        barWidth: "60%"
      }
    ],
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    }
  };
});

// 业绩分类饼图配置
const classificationChartOption = computed<ECOption>(() => {
  const colors = ["#409EFF", "#67C23A", "#E6A23C", "#F56C6C", "#909399", "#C0C4CC"];

  return {
    tooltip: {
      trigger: "item",
      formatter: "{a}<br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      right: "10%",
      top: "center",
      itemWidth: 12,
      itemHeight: 12
    },
    series: [
      {
        name: "业绩分类",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["40%", "50%"],
        data: classificationData.value.map((item, index) => ({
          ...item,
          itemStyle: {
            color: colors[index % colors.length]
          }
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: false
        }
      }
    ]
  };
});

// 获取业绩数据
const getPerformanceData = async () => {
  loading.value = true;
  try {
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500));

    // 根据时间类型生成不同的数据
    if (timeType.value === "month") {
      const daysInMonth = dayjs(selectedTime.value).daysInMonth();
      performanceData.value = Array.from({ length: daysInMonth }, (_, i) => ({
        date: `${i + 1}日`,
        amount: Math.floor(Math.random() * 10000) + 1000
      }));
    } else if (timeType.value === "week") {
      performanceData.value = Array.from({ length: 7 }, (_, i) => ({
        date: dayjs().startOf('week').add(i, 'day').format('MM-DD'),
        amount: Math.floor(Math.random() * 5000) + 500
      }));
    } else {
      performanceData.value = Array.from({ length: 12 }, (_, i) => ({
        date: `${i + 1}月`,
        amount: Math.floor(Math.random() * 50000) + 10000
      }));
    }

    // 业绩分类模拟数据
    classificationData.value = [
      { name: "基础课程", value: 35 },
      { name: "进阶课程", value: 25 },
      { name: "高级课程", value: 20 },
      { name: "专业课程", value: 12 },
      { name: "VIP课程", value: 5 },
      { name: "其他所有类型", value: 3 }
    ];
  } catch (error) {
    console.error("获取业绩数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 时间类型变化
const handleTimeTypeChange = (type: TimeType) => {
  timeType.value = type;
  // 重置时间选择
  switch (type) {
    case "week":
      selectedTime.value = dayjs().format("YYYY-[W]WW");
      break;
    case "month":
      selectedTime.value = dayjs().format("YYYY-MM");
      break;
    case "year":
      selectedTime.value = dayjs().format("YYYY");
      break;
  }
};

// 监听时间变化
watch([timeType, selectedTime], () => {
  getPerformanceData();
});

// 页面初始化
onMounted(() => {
  getPerformanceData();
});
</script>

<template>
  <el-card v-loading="loading" class="performance-card">
    <template #header>
      <div class="card-header">
        <span class="card-title">我的业绩</span>
        <div class="time-controls">
          <!-- 时间类型选择 -->
          <el-radio-group
            :model-value="timeType"
            size="small"
            @update:model-value="handleTimeTypeChange"
          >
            <el-radio-button label="week">周</el-radio-button>
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="year">年</el-radio-button>
          </el-radio-group>

          <!-- 时间选择器 -->
          <el-date-picker
            v-model="selectedTime"
            :type="timePickerConfig.type"
            :format="timePickerConfig.format"
            :value-format="timePickerConfig.valueFormat"
            :placeholder="timePickerConfig.placeholder"
            :disabled-date="disableFutureTime"
            :clearable="false"
            size="small"
            style="width: 140px; margin-left: 10px;"
          />
        </div>
      </div>
    </template>

    <!-- 业绩图表 -->
    <div class="chart-container">
      <div class="chart-section">
        <h4 class="chart-title">我的业绩</h4>
        <Echarts :option="performanceChartOption" height="300" />
      </div>

      <div class="chart-section">
        <h4 class="chart-title">业绩分类</h4>
        <Echarts :option="classificationChartOption" height="300" />
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped>
.performance-card {
  height: 500px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .time-controls {
      display: flex;
      align-items: center;
    }
  }

  .chart-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    height: calc(100% - 60px);

    .chart-section {
      .chart-title {
        margin: 0 0 10px 0;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .performance-card {
    .card-header {
      flex-direction: column;
      gap: 10px;
      align-items: stretch;
    }

    .chart-container {
      grid-template-columns: 1fr;
      gap: 10px;
    }
  }
}
</style>
