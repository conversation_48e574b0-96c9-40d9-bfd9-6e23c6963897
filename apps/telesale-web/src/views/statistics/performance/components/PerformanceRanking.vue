<!--
 * @Date         : 2025-07-17 16:00:00
 * @Description  : 业绩排名组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { computed } from "vue";
import dayjs from "dayjs";

interface RankingData {
  myRanking: number;
  monthlyPerformance: number;
  monthlyTarget: number;
  progress: number;
}

interface Props {
  selectedMonth: string;
  rankingData: RankingData;
  disableFutureMonths: (time: Date) => boolean;
}

interface Emits {
  (e: "update:selectedMonth", value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算进度条样式和文案
const progressInfo = computed(() => {
  const { monthlyTarget, progress } = props.rankingData;
  
  if (monthlyTarget === 0) {
    return {
      showProgress: false,
      text: "没有预设的终点，才更有无限可能 —— 从今天起，每一次成交都是惊喜，每一步成长都是答案！",
      color: "#409EFF"
    };
  }
  
  let text = "";
  let color = "#409EFF";
  
  if (progress >= 100) {
    text = "巅峰之上，再造传奇！";
    color = "#F56C6C";
  } else if (progress >= 90) {
    text = "破晓时刻，全力绝杀！";
    color = "#E6A23C";
  } else if (progress >= 75) {
    text = "雷霆之势，碾碎阻碍！";
    color = "#E6A23C";
  } else if (progress >= 50) {
    text = "势如破竹，乘胜追击！";
    color = "#67C23A";
  } else if (progress >= 25) {
    text = "破冰启航，加速狂飙！";
    color = "#409EFF";
  } else {
    text = "破冰启航，加速狂飙！";
    color = "#909399";
  }
  
  return {
    showProgress: true,
    text,
    color
  };
});

// 格式化数字显示
const formatNumber = (num: number) => {
  return num.toLocaleString();
};

// 月份选择器变化
const handleMonthChange = (value: string) => {
  emit("update:selectedMonth", value);
};
</script>

<template>
  <el-card class="ranking-card">
    <div class="ranking-header">
      <!-- 我的业绩排名 -->
      <div class="ranking-badge">
        我的业绩排名：NO.{{ rankingData.myRanking || '--' }}
      </div>
      
      <!-- 本月业绩 -->
      <div class="performance-info">
        <span class="label">本月业绩：</span>
        <span class="value">{{ formatNumber(rankingData.monthlyPerformance) }}</span>
      </div>
      
      <!-- 进度条区域 -->
      <div class="progress-section">
        <div v-if="progressInfo.showProgress" class="progress-container">
          <el-progress 
            :percentage="Math.min(rankingData.progress, 100)"
            :color="progressInfo.color"
            :stroke-width="8"
            :show-text="false"
          />
          <div class="progress-text" :style="{ color: progressInfo.color }">
            {{ Math.round(rankingData.progress) }}%
          </div>
        </div>
        <div class="motivational-text" :style="{ color: progressInfo.color }">
          {{ progressInfo.text }}
        </div>
      </div>
      
      <!-- 本月目标 -->
      <div class="target-info">
        <span class="label">本月目标：</span>
        <span class="value">{{ formatNumber(rankingData.monthlyTarget) }}</span>
      </div>
      
      <!-- 月份选择器 -->
      <div class="month-selector">
        <el-date-picker
          :model-value="selectedMonth"
          type="month"
          placeholder="选择月份"
          format="YYYY-MM"
          value-format="YYYY-MM"
          :disabled-date="disableFutureMonths"
          :clearable="false"
          @update:model-value="handleMonthChange"
        />
      </div>
    </div>
  </el-card>
</template>

<style lang="scss" scoped>
.ranking-card {
  margin-bottom: 20px;
  
  .ranking-header {
    display: flex;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
    
    .ranking-badge {
      background: #409EFF;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 14px;
    }
    
    .performance-info,
    .target-info {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .label {
        color: #606266;
        font-size: 14px;
      }
      
      .value {
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }
    }
    
    .progress-section {
      flex: 1;
      min-width: 300px;
      
      .progress-container {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 8px;
        
        :deep(.el-progress) {
          flex: 1;
        }
        
        .progress-text {
          font-weight: 600;
          font-size: 14px;
          min-width: 40px;
        }
      }
      
      .motivational-text {
        font-size: 13px;
        font-weight: 500;
        text-align: center;
        line-height: 1.4;
      }
    }
    
    .month-selector {
      margin-left: auto;
      
      :deep(.el-date-editor) {
        width: 140px;
      }
    }
  }
}

@media (max-width: 768px) {
  .ranking-card .ranking-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
    
    .month-selector {
      margin-left: 0;
      align-self: flex-end;
    }
    
    .progress-section {
      min-width: auto;
    }
  }
}
</style>
