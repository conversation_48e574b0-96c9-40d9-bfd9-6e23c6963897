/**
 * @Date         : 2025-07-11 17:05:00
 * @Description  : 音频管理Hook
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ElMessage } from "element-plus";
import {
  WorkerTrainingTaskCourseInstance,
  getWorkerTrainingTaskCourseInfo
} from "/@/api/AIQualityInspection/taskManagement";
import { useEvaluationStore } from "../store";

/**
 * 音频管理Hook
 */
export function useAudioManager() {
  const store = useEvaluationStore();

  /**
   * 检查音频URL是否可播放
   * @param audioUrl 音频URL
   * @returns Promise<boolean> 是否可播放
   */
  async function checkAudioPlayable(audioUrl: string): Promise<boolean> {
    return new Promise(resolve => {
      const audio = document.createElement("audio") as HTMLAudioElement;

      // 设置超时时间（5秒）
      const timeout = setTimeout(() => {
        console.log("⏰ 音频检查超时");
        audio.removeEventListener("canplaythrough", onCanPlay);
        audio.removeEventListener("error", onError);
        resolve(false);
      }, 5000);

      const onCanPlay = () => {
        console.log("✅ 音频可以播放");
        clearTimeout(timeout);
        audio.removeEventListener("canplaythrough", onCanPlay);
        audio.removeEventListener("error", onError);
        resolve(true);
      };

      const onError = (error: Event) => {
        console.log("❌ 音频加载失败:", error);
        clearTimeout(timeout);
        audio.removeEventListener("canplaythrough", onCanPlay);
        audio.removeEventListener("error", onError);
        resolve(false);
      };

      audio.addEventListener("canplaythrough", onCanPlay);
      audio.addEventListener("error", onError);
      audio.src = audioUrl;
    });
  }

  /**
   * 检查合成音频的可播放性
   * @param skipLoadingState 是否跳过加载状态设置（用于轮询时避免闪烁）
   */
  async function checkMergeAudioPlayable(skipLoadingState = false) {
    // 在纯文本模式下跳过音频检查
    if (store.isTextOnlyMode.value) {
      console.log("纯文本模式，跳过音频可播放性检查");
      store.audioPlayable.value = null;
      return;
    }

    if (!store.conversationData.value?.mergeAudioURL) {
      store.audioPlayable.value = null;
      return;
    }

    // 轮询时跳过加载状态设置，避免UI闪烁
    if (!skipLoadingState) {
      store.audioCheckLoading.value = true;
      store.audioPlayable.value = null;
    }

    try {
      const logPrefix = skipLoadingState
        ? "🔄 轮询检查音频可播放性:"
        : "开始检查音频可播放性:";
      console.log(logPrefix, store.conversationData.value.mergeAudioURL);

      const isPlayable = await checkAudioPlayable(
        store.conversationData.value.mergeAudioURL
      );

      // 只有状态真正变化时才更新
      if (store.audioPlayable.value !== isPlayable) {
        store.audioPlayable.value = isPlayable;

        console.log("✅ 合并音频可播放性检查完成:", {
          url: store.conversationData.value.mergeAudioURL,
          playable: isPlayable,
          timestamp: new Date().toISOString(),
          skipLoadingState
        });

        // 根据检查结果显示不同的日志
        if (isPlayable) {
          console.log("🎵 音频可以正常播放");
        } else {
          console.warn("⚠️ 音频无法播放，可能正在合成中");
        }

        // 如果是轮询检查且音频变为可播放，显示成功消息
        if (skipLoadingState && isPlayable) {
          ElMessage.success("音频已可播放！");
        }
      } else {
        console.log(`🔄 音频状态未变化: ${isPlayable ? "可播放" : "不可播放"}`);
      }
    } catch (error) {
      console.error("❌ 检查音频可播放性失败:", error);
      store.audioPlayable.value = false;
    } finally {
      // 轮询时不修改加载状态
      if (!skipLoadingState) {
        store.audioCheckLoading.value = false;
      }
    }
  }

  /**
   * 刷新音频数据
   */
  async function refreshAudioData(courseInfo: WorkerTrainingTaskCourseInstance) {
    if (
      !courseInfo ||
      !courseInfo.workerTrainingTaskId ||
      !courseInfo.trainingTaskCourseId
    ) {
      ElMessage.error("缺少必要的课程信息");
      return;
    }

    // 获取对话ID
    let workerTrainingConversationId: string | undefined;

    if (store.selectedPracticeRecord.value?.historicalItem?.conversation?.id) {
      // 如果有选中的历史练习记录，使用其对话ID
      workerTrainingConversationId =
        store.selectedPracticeRecord.value.historicalItem.conversation.id;
    } else if (courseInfo.workerTrainingConversationId) {
      // 优先使用props中的对话ID
      workerTrainingConversationId = courseInfo.workerTrainingConversationId;
    } else if (store.conversationData.value?.id) {
      // 否则使用当前对话数据的ID
      workerTrainingConversationId = store.conversationData.value.id;
    }

    if (!workerTrainingConversationId) {
      ElMessage.error("缺少对话ID信息");
      return;
    }

    store.loading.value = true;

    try {
      // 重新调用接口获取最新的课程详情
      const response = await getWorkerTrainingTaskCourseInfo({
        workerTrainingTaskId: courseInfo.workerTrainingTaskId,
        trainingTaskCourseId: courseInfo.trainingTaskCourseId,
        taskMode: store.taskMode.value,
        workerTrainingConversationId
      });

      if (response?.data?.conversation) {
        // 更新会话数据
        store.setConversationData(response.data.conversation);

        // 检查音频可播放性
        await checkMergeAudioPlayable();

        // 检查是否需要启动轮询
        if (shouldStartDataPolling()) {
          console.log("📊 刷新后检测到任务得分或音频URL仍未完成，启动数据轮询");
          startDataPolling(courseInfo);
        }

        if (shouldStartAudioPolling()) {
          console.log("📊 刷新后检测到音频不可播放，启动音频轮询");
          startAudioPolling();
        }

        // 根据检查结果显示消息
        if (store.conversationData.value?.mergeAudioURL) {
          if (store.audioPlayable.value) {
            ElMessage.success("音频已生成，可以播放了！");
          } else {
            ElMessage.info("音频仍在合成中，请稍后刷新查看");
          }
        } else {
          ElMessage.info("音频仍在生成中，请稍后再试");
        }
      } else {
        ElMessage.error("未找到课程对话记录");
      }
    } catch (error) {
      console.error("刷新音频数据失败:", error);
      ElMessage.error("刷新音频数据失败");
    } finally {
      store.loading.value = false;
    }
  }

  /**
   * 判断是否需要启动数据轮询
   */
  function shouldStartDataPolling(): boolean {
    let hasScore = false;

    // 练习模式下，检查当前选中的练习记录是否有得分
    if (store.isPracticeMode.value && store.selectedPracticeRecord.value) {
      const selectedRecord = store.selectedPracticeRecord.value;
      hasScore = selectedRecord.score !== null &&
                 selectedRecord.score !== undefined &&
                 selectedRecord.score !== "" &&
                 selectedRecord.score !== "未评分";
      console.log("练习模式 - 当前选中练习记录得分状态:", {
        recordId: selectedRecord.id,
        score: selectedRecord.score,
        hasScore
      });
    } else {
      // 非练习模式或没有选中记录，检查对话数据的AI评估得分
      hasScore = !!store.conversationData.value?.aiAppraise?.score;
      console.log("非练习模式 - AI评估得分状态:", {
        score: store.conversationData.value?.aiAppraise?.score,
        hasScore
      });
    }

    // 检查合成音频URL是否缺失（仅在非纯文本模式下检查）
    const hasAudioURL =
      store.isTextOnlyMode.value || store.conversationData.value?.mergeAudioURL;

    console.log("轮询条件检查:", {
      hasScore,
      hasAudioURL,
      isTextOnlyMode: store.isTextOnlyMode.value,
      isPracticeMode: store.isPracticeMode.value,
      needsPolling: !hasScore || !hasAudioURL
    });

    // 如果任务得分或音频URL缺失，则需要数据轮询
    return !hasScore || !hasAudioURL;
  }

  /**
   * 检查是否需要音频可播放性轮询
   * @returns 是否需要音频可播放性轮询
   */
  function shouldStartAudioPolling(): boolean {
    if (!store.conversationData.value || store.isTextOnlyMode.value) return false;

    // 只有当有音频URL但不可播放时，才需要音频轮询
    const hasAudioURL = store.conversationData.value.mergeAudioURL;
    const isAudioPlayable = store.audioPlayable.value === true;

    return hasAudioURL && !isAudioPlayable;
  }

  /**
   * 启动数据轮询
   */
  function startDataPolling(courseInfo: WorkerTrainingTaskCourseInstance) {
    if (store.isDataPolling.value) {
      console.log("📊 数据轮询已在进行中，跳过启动");
      return;
    }

    console.log("📊 启动数据轮询");
    store.isDataPolling.value = true;

    store.dataPollingTimer.value = setInterval(async () => {
      await pollDataUpdates(courseInfo);
    }, 1000); // 每秒轮询一次
  }

  /**
   * 启动音频可播放性轮询
   */
  function startAudioPolling() {
    if (store.isAudioPolling.value || !shouldStartAudioPolling()) {
      return;
    }

    console.log("🔄 启动音频可播放性轮询");
    store.isAudioPolling.value = true;

    store.audioPollingTimer.value = setInterval(async () => {
      try {
        await pollAudioPlayability();
      } catch (error) {
        console.error("音频轮询失败:", error);
      }
    }, 1000); // 每秒轮询一次
  }

  /**
   * 轮询音频可播放性
   */
  async function pollAudioPlayability() {
    if (!store.conversationData.value?.mergeAudioURL || store.isTextOnlyMode.value) {
      console.warn("音频轮询时缺少音频URL或为纯文本模式，停止音频轮询");
      store.stopAudioPolling();
      return;
    }

    try {
      console.log("🔄 检查音频可播放性");
      const previousPlayableState = store.audioPlayable.value;

      // 轮询时跳过加载状态，避免UI闪烁
      await checkMergeAudioPlayable(true);

      // 如果音频状态从不可播放变为可播放，显示成功消息并停止轮询
      if (previousPlayableState === false && store.audioPlayable.value === true) {
        ElMessage.success("音频已可播放！");
        store.stopAudioPolling();
      }
      // 如果音频可播放，停止轮询
      else if (store.audioPlayable.value === true) {
        console.log("✅ 音频已可播放，停止音频轮询");
        store.stopAudioPolling();
      }
    } catch (error) {
      console.error("音频可播放性轮询失败:", error);
      // 轮询失败不显示错误消息，避免频繁弹窗
    }
  }

  /**
   * 轮询数据更新（任务得分和音频URL）
   */
  async function pollDataUpdates(courseInfo: WorkerTrainingTaskCourseInstance) {
    if (
      !courseInfo ||
      !courseInfo.workerTrainingTaskId ||
      !courseInfo.trainingTaskCourseId
    ) {
      console.warn("数据轮询时缺少必要的课程信息，停止数据轮询");
      store.stopDataPolling();
      return;
    }

    // 设置轮询loading状态，但不影响主loading状态
    store.pollingLoading.value = true;

    try {
      // 获取对话ID
      let workerTrainingConversationId: string | undefined;

      if (store.selectedPracticeRecord.value?.historicalItem?.conversation?.id) {
        // 如果有选中的历史练习记录，使用其对话ID
        workerTrainingConversationId =
          store.selectedPracticeRecord.value.historicalItem.conversation.id;
      } else if (courseInfo.workerTrainingConversationId) {
        // 优先使用props中的对话ID
        workerTrainingConversationId = courseInfo.workerTrainingConversationId;
      } else if (store.conversationData.value?.id) {
        // 否则使用当前对话数据的ID
        workerTrainingConversationId = store.conversationData.value.id;
      }

      if (!workerTrainingConversationId) {
        console.warn("数据轮询时缺少对话ID，停止数据轮询");
        store.stopDataPolling();
        return;
      }

      // 调用接口获取最新的课程详情
      const response = await getWorkerTrainingTaskCourseInfo({
        workerTrainingTaskId: courseInfo.workerTrainingTaskId,
        trainingTaskCourseId: courseInfo.trainingTaskCourseId,
        taskMode: store.taskMode.value,
        workerTrainingConversationId
      });

      if (response?.data?.conversation) {
        const newConversationData = response.data.conversation;

        // 检查是否有新的任务得分
        let hasNewScore = false;

        if (store.isPracticeMode.value && store.selectedPracticeRecord.value) {
          // 练习模式：检查练习记录的得分是否有变化
          const currentRecordScore = store.selectedPracticeRecord.value.score;
          const newScore = newConversationData.aiAppraise?.score;

          // 如果新得分存在且与当前记录得分不同，则认为有新得分
          hasNewScore = newScore && (
            !currentRecordScore ||
            currentRecordScore === "未评分" ||
            currentRecordScore !== newScore
          );

          console.log("练习模式 - 得分变化检查:", {
            recordId: store.selectedPracticeRecord.value.id,
            currentRecordScore,
            newScore,
            hasNewScore
          });
        } else {
          // 非练习模式：检查对话数据的得分是否有变化
          hasNewScore =
            newConversationData.aiAppraise?.score &&
            !store.conversationData.value?.aiAppraise?.score;

          console.log("非练习模式 - 得分变化检查:", {
            currentScore: store.conversationData.value?.aiAppraise?.score,
            newScore: newConversationData.aiAppraise?.score,
            hasNewScore
          });
        }

        // 检查是否有新的合成音频URL
        const hasNewAudio =
          newConversationData.mergeAudioURL &&
          !store.conversationData.value?.mergeAudioURL;

        // 更新会话数据
        store.setConversationData(newConversationData);

        // 如果有新的评估结果，更新评估信息
        if (hasNewScore) {
          console.log(
            "✅ 检测到新的任务得分:",
            newConversationData.aiAppraise?.score
          );
          store.updateEvaluationInfo(newConversationData.aiAppraise);

          // 在练习模式下，同时更新当前选中的练习记录的得分
          if (store.isPracticeMode.value && store.selectedPracticeRecord.value) {
            const newScore = newConversationData.aiAppraise?.score;
            if (newScore) {
              console.log("🔄 同步更新练习记录得分:", {
                recordId: store.selectedPracticeRecord.value.id,
                oldScore: store.selectedPracticeRecord.value.score,
                newScore
              });
              store.selectedPracticeRecord.value.score = newScore;
            }
          }

          ElMessage.success("评估结果已更新！");
        }

        // 如果有新的音频URL，显示消息并启动音频可播放性轮询
        if (hasNewAudio && !store.isTextOnlyMode.value) {
          console.log("✅ 检测到新的合成音频URL");
          ElMessage.success("合成音频已生成，正在检查可播放性...");

          // 启动音频可播放性轮询
          startAudioPolling();
        }

        // 检查是否可以停止轮询
        if (!shouldStartDataPolling()) {
          console.log("✅ 任务得分和音频URL都已完成，停止数据轮询");
          store.stopDataPolling();
        }
      }
    } catch (error) {
      console.error("❌ 数据轮询失败:", error);
      // 轮询失败时不停止轮询，继续尝试
    } finally {
      // 清除轮询loading状态
      store.pollingLoading.value = false;
    }
  }

  return {
    checkAudioPlayable,
    checkMergeAudioPlayable,
    refreshAudioData,
    shouldStartDataPolling,
    shouldStartAudioPolling,
    startDataPolling,
    startAudioPolling,
    pollAudioPlayability,
    pollDataUpdates
  };
}
