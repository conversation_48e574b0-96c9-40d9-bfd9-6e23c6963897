/**
 * @Date         : 2025-07-11 17:00:00
 * @Description  : 评估数据管理Hook
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ElMessage } from "element-plus";
import {
  WorkerTrainingTaskCourseInstance,
  getWorkerTrainingTaskCourseInfo,
  getWorkerTrainingTaskInfo,
  getWorkerTrainingTaskCourseHistoricalData,
  TASK_MODE
} from "/@/api/AIQualityInspection/taskManagement";
import { getMget } from "/@/api/AISupport/Library";
import { TagInstance } from "/@/api/AIQualityInspection/excellentCases";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";
import { useEvaluationStore, type ExtendedPracticeRecord } from "../store";

/**
 * 评估数据管理Hook
 */
export function useEvaluationData() {
  const store = useEvaluationStore();

  /**
   * 获取任务信息和练习记录
   */
  async function fetchTaskInfo(courseInfo: WorkerTrainingTaskCourseInstance) {
    if (!courseInfo?.workerTrainingTaskId) {
      console.warn("缺少任务ID，无法获取任务信息");
      return;
    }

    try {
      const response = await getWorkerTrainingTaskInfo(
        Number(courseInfo.workerTrainingTaskId)
      );

      if (response?.data?.item?.task) {
        const task = response.data.item.task;
        store.taskMode.value = task.taskMode || TASK_MODE.EXAM;
        store.taskName.value = task.name || "";

        // 如果是练习模式，获取历史练习记录
        if (store.taskMode.value === TASK_MODE.PRACTICE) {
          await fetchHistoricalData(courseInfo);
          // 将历史数据转换为练习记录格式
          store.practiceRecords.value =
            convertHistoricalDataToPracticeRecords();
          // 默认选择最后一条记录（按时间排序的最新记录）
          if (store.practiceRecords.value.length > 0) {
            store.selectedPracticeRecord.value =
              store.practiceRecords.value.find(r => r.isLatest) ||
              store.practiceRecords.value[0];


          }
        } else {
          store.practiceRecords.value = [];
          store.selectedPracticeRecord.value = null;
        }
      }
    } catch (error) {
      console.error("获取任务信息失败:", error);
    }
  }

  /**
   * 获取历史练习数据
   */
  async function fetchHistoricalData(
    courseInfo: WorkerTrainingTaskCourseInstance
  ) {
    if (
      !courseInfo?.workerTrainingTaskId ||
      !courseInfo?.trainingTaskCourseId
    ) {
      console.warn("缺少必要的课程ID信息，无法获取历史数据");
      return;
    }

    store.historicalDataLoading.value = true;

    try {
      const response = await getWorkerTrainingTaskCourseHistoricalData({
        workerTrainingTaskId: String(courseInfo.workerTrainingTaskId),
        trainingTaskCourseId: String(courseInfo.trainingTaskCourseId)
      });

      if (response?.data?.courses) {
        store.historicalData.value = response.data.courses;
        console.log("获取历史练习数据成功:", store.historicalData.value);
      } else {
        store.historicalData.value = [];
        console.log("暂无历史练习数据");
      }
    } catch (error) {
      console.error("获取历史练习数据失败:", error);
      ElMessage.error("获取历史练习数据失败");
      store.historicalData.value = [];
    } finally {
      store.historicalDataLoading.value = false;
    }
  }

  /**
   * 将历史练习数据转换为练习记录格式
   */
  function convertHistoricalDataToPracticeRecords(): ExtendedPracticeRecord[] {
    if (
      !store.historicalData.value ||
      store.historicalData.value.length === 0
    ) {
      return [];
    }

    // 先按时间排序，然后转换为练习记录格式
    const sortedData = store.historicalData.value
      .filter(item => item.finished) // 只显示已完成的记录
      .sort(
        (a, b) =>
          new Date(b.finishedAt || 0).getTime() - new Date(a.finishedAt || 0).getTime()
      ); // 按时间倒序排列，最新的在前面

    return sortedData.map((item, index) => ({
      id: `historical-${item.workerTrainingTaskId}-${item.trainingTaskCourseId}-${index}`,
      practiceCount: sortedData.length - index, // 最新的是第N次，最早的是第1次
      score: item.score || "未评分",
      finishedAt: item.finishedAt || new Date().toISOString(),
      isLatest: index === 0, // 排序后的第一个就是最新的
      duration: item.conversation?.duration || 0,
      historicalItem: item
    }));
  }

  /**
   * 获取课程详情
   */
  async function fetchCourseInfo(
    courseInfo: WorkerTrainingTaskCourseInstance,
    targetRecord?: ExtendedPracticeRecord
  ) {
    if (
      !courseInfo?.workerTrainingTaskId ||
      !courseInfo?.trainingTaskCourseId
    ) {
      ElMessage.error("缺少必要的课程信息");
      return;
    }

    store.loading.value = true;

    try {
      // 获取对话ID
      let workerTrainingConversationId: string | undefined;

      if (targetRecord?.historicalItem?.conversation?.id) {
        // 如果有选中的历史练习记录，使用其对话ID
        workerTrainingConversationId =
          targetRecord.historicalItem.conversation.id;
        console.log("📊 使用选中练习记录的对话ID:", {
          practiceCount: targetRecord.practiceCount,
          conversationId: workerTrainingConversationId
        });
      } else if (courseInfo.workerTrainingConversationId) {
        // 优先使用props中的对话ID（练习完成时已通过历史数据接口更新为最新的对话ID）
        workerTrainingConversationId = courseInfo.workerTrainingConversationId;
        console.log("📊 使用courseInfo中的对话ID（最新练习）:", workerTrainingConversationId);
      } else if (store.conversationData.value?.id) {
        // 否则使用当前对话数据的ID
        workerTrainingConversationId = store.conversationData.value.id;
        console.log("📊 使用store中的对话ID:", workerTrainingConversationId);
      }

      if (!workerTrainingConversationId) {
        ElMessage.error("缺少对话ID信息");
        return;
      }

      // 调用接口获取课程详情
      const response = await getWorkerTrainingTaskCourseInfo({
        workerTrainingTaskId: courseInfo.workerTrainingTaskId,
        trainingTaskCourseId: courseInfo.trainingTaskCourseId,
        taskMode: store.taskMode.value,
        workerTrainingConversationId
      });

      if (response?.data?.conversation) {
        const newConversationData = response.data.conversation;

        // 更新会话数据
        store.setConversationData(newConversationData);
        store.setCourseInfo(courseInfo);

        // 更新评估相关数据
        if (newConversationData.aiAppraise) {
          store.updateEvaluationInfo(newConversationData.aiAppraise);
        } else {
          // 如果没有AI评估结果，将评估数据置空
          store.evaluationLevel.value = null;
          store.evaluationResult.value = "";
          store.evaluationReason.value = "";
          store.evaluationStrengths.value = [];
          store.evaluationImprovements.value = [];
        }

        console.log("✅ 课程数据更新完成");
        return newConversationData;
      } else {
        ElMessage.error("未找到课程对话记录");
      }
    } catch (error) {
      console.error("获取课程详情失败:", error);
      ElMessage.error("获取课程详情失败");
    } finally {
      store.loading.value = false;
    }
  }

  /**
   * 获取用户标签
   */
  async function fetchUserTags() {
    if (
      !store.courseData.value?.course?.course?.referenceQas ||
      store.courseData.value.course.course.referenceQas.length === 0
    ) {
      store.userTags.value = [];
      return;
    }

    // 提取所有referenceQaId
    const referenceQaIds = store.courseData.value.course.course.referenceQas
      .map(qa => qa.referenceQaId)
      .filter(id => id); // 过滤掉空值

    if (referenceQaIds.length === 0) {
      store.userTags.value = [];
      return;
    }

    store.tagsLoading.value = true;
    try {
      const response = await getMget({
        libraryUUID: libraryInfo.libraryUUID,
        ids: referenceQaIds
      });

      // 定义返回数据类型
      interface TagGroupInstance {
        id: number;
        name: string;
        key: string;
        tags: TagInstance[];
      }

      interface KnowledgeItem {
        id: string;
        tags?: TagGroupInstance[];
      }

      interface ResponseData {
        list: KnowledgeItem[];
      }

      // 提取所有标签数据
      const allTags: TagInstance[] = [];
      const responseData = response.data as ResponseData;
      if (responseData?.list) {
        responseData.list.forEach((item: KnowledgeItem) => {
          if (item.tags && Array.isArray(item.tags)) {
            // 从标签组中提取实际的标签
            item.tags.forEach((tagGroup: TagGroupInstance) => {
              if (tagGroup.tags && Array.isArray(tagGroup.tags)) {
                allTags.push(...tagGroup.tags);
              }
            });
          }
        });
      }

      // 去重标签（根据key去重）
      const uniqueTags = allTags.filter(
        (tag, index, self) => index === self.findIndex(t => t.key === tag.key)
      );

      store.userTags.value = uniqueTags;
    } catch (error) {
      console.error("获取用户标签失败:", error);
      ElMessage.error("获取用户标签失败");
      store.userTags.value = [];
    } finally {
      store.tagsLoading.value = false;
    }
  }

  return {
    fetchTaskInfo,
    fetchHistoricalData,
    convertHistoricalDataToPracticeRecords,
    fetchCourseInfo,
    fetchUserTags
  };
}
