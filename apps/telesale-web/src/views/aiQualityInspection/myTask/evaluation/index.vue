<!--
 * @Date         : 2025-05-21 16:45:00
 * @Description  : 课程评估抽屉 - 重构版本
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="drawerVisible"
    :title="courseData?.course?.course?.name || '课程评估'"
    size="90%"
    destroy-on-close
    direction="rtl"
    @open="handleDrawerOpen"
    @close="handleClose"
  >
    <div class="evaluation-drawer" v-loading="loading">
      <!-- 评估内容区域 -->
      <div class="flex gap-20px h-full">
        <!-- 左侧对话记录区域 -->
        <ChatRecord
          ref="chatRecordRef"
          @refresh-audio="handleRefreshAudio"
          @practice-record-change="handlePracticeRecordChange"
        />

        <!-- 右侧评估结果区域 -->
        <EvaluationResult
          :is-admin-view="props.isAdminView"
          @practice-record-change="handlePracticeRecordChange"
          @start-practice="handleStartPractice"
        />
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineModel, defineProps, onUnmounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { WorkerTrainingTaskCourseInstance } from "/@/api/AIQualityInspection/taskManagement";
import ChatRecord from "./components/ChatRecord.vue";
import EvaluationResult from "./components/EvaluationResult.vue";
import { useEvaluationStore, type ExtendedPracticeRecord } from "./store";
import { useEvaluationData, useAudioManager } from "./hooks";

// 使用defineModel实现双向绑定
const drawerVisible = defineModel<boolean>("visible", { default: false });

// 添加调试监听
watch(drawerVisible, (newValue, oldValue) => {
  console.log("📊 评估抽屉可见性变化:", { oldValue, newValue });
});

// 定义props
const props = defineProps<{
  courseInfo: WorkerTrainingTaskCourseInstance | null;
  /** 是否为管理员视角 */
  isAdminView?: boolean;
}>();

// 定义事件
const emit = defineEmits<{
  close: [];
  startPractice: [courseInfo: WorkerTrainingTaskCourseInstance];
}>();

// 使用统一状态管理
const store = useEvaluationStore();

// 使用业务hooks
const evaluationData = useEvaluationData();
const audioManager = useAudioManager();

// 解构状态
const { loading, courseData, stopAllPolling } = store;

// 组件引用
const chatRecordRef = ref<InstanceType<typeof ChatRecord>>();

/**
 * 抽屉打开事件处理
 */
async function handleDrawerOpen() {
  if (
    !props.courseInfo ||
    !props.courseInfo.workerTrainingTaskId ||
    !props.courseInfo.trainingTaskCourseId
  ) {
    ElMessage.error("缺少必要的课程信息");
    return;
  }

  loading.value = true;

  try {
    // 获取任务信息和练习记录
    await evaluationData.fetchTaskInfo(props.courseInfo);

    // 获取课程详情 - 传入选中的练习记录以确保获取正确的详情
    const conversationData = await evaluationData.fetchCourseInfo(
      props.courseInfo,
      store.selectedPracticeRecord.value || undefined
    );

    if (conversationData) {
      // 检查音频可播放性
      if (conversationData.mergeAudioURL) {
        console.log("检测到音频URL，开始检查可播放性...");
        await audioManager.checkMergeAudioPlayable();
      } else {
        store.audioPlayable.value = null;
      }

      // 获取用户标签
      await evaluationData.fetchUserTags();

      // 检查是否需要启动轮询
      if (audioManager.shouldStartDataPolling()) {
        console.log("📊 检测到任务得分或音频URL未完成，启动数据轮询");
        audioManager.startDataPolling(props.courseInfo);
      }

      if (audioManager.shouldStartAudioPolling()) {
        console.log("📊 检测到音频不可播放，启动音频轮询");
        audioManager.startAudioPolling();
      }

      if (
        !audioManager.shouldStartDataPolling() &&
        !audioManager.shouldStartAudioPolling()
      ) {
        console.log("✅ 任务得分和合成音频都已完成，无需轮询");
      }

      // 滚动到对话开始位置
      chatRecordRef.value?.scrollToTop();
    } else {
      ElMessage.error("未找到课程对话记录");
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    ElMessage.error("获取课程详情失败");
  } finally {
    loading.value = false;
  }
}

/**
 * 处理刷新音频
 */
async function handleRefreshAudio() {
  if (!props.courseInfo) {
    ElMessage.error("缺少课程信息");
    return;
  }
  await audioManager.refreshAudioData(props.courseInfo);
}

/**
 * 处理练习记录选择变化
 */
async function handlePracticeRecordChange(record: ExtendedPracticeRecord) {
  store.selectedPracticeRecord.value = record;

  // 获取该记录的详细数据
  if (props.courseInfo) {
    const conversationData = await evaluationData.fetchCourseInfo(
      props.courseInfo,
      record
    );

    if (conversationData) {
      // 检查音频可播放性
      if (conversationData.mergeAudioURL) {
        console.log("切换练习记录后检测到音频URL，开始检查可播放性...");
        await audioManager.checkMergeAudioPlayable();
      } else {
        store.audioPlayable.value = null;
      }

      // 检查是否需要启动轮询（重要：切换练习记录后也需要检查轮询）
      if (audioManager.shouldStartDataPolling()) {
        console.log(
          "📊 切换练习记录后检测到任务得分或音频URL未完成，启动数据轮询"
        );
        audioManager.startDataPolling(props.courseInfo);
      }

      if (audioManager.shouldStartAudioPolling()) {
        console.log("📊 切换练习记录后检测到音频不可播放，启动音频轮询");
        audioManager.startAudioPolling();
      }

      if (
        !audioManager.shouldStartDataPolling() &&
        !audioManager.shouldStartAudioPolling()
      ) {
        console.log("✅ 切换练习记录后任务得分和合成音频都已完成，无需轮询");
      }
    }

    // 滚动到对话开始位置
    chatRecordRef.value?.scrollToTop();
  }
}

/**
 * 处理开始练习
 */
function handleStartPractice() {
  if (!props.courseInfo) {
    ElMessage.error("课程信息不能为空");
    return;
  }

  // 关闭评估抽屉
  drawerVisible.value = false;

  // 触发开始练习事件，传递课程信息
  emit("startPractice", props.courseInfo);
}

/**
 * 关闭抽屉
 */
function handleClose() {
  console.log("🔒 评估抽屉关闭事件触发");

  // 停止所有轮询
  stopAllPolling();

  // 触发关闭事件
  emit("close");

  console.log("✅ 评估抽屉关闭处理完成");
}

// 组件卸载时清理轮询
onUnmounted(() => {
  stopAllPolling();
});
</script>

<style scoped>
.evaluation-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .evaluation-drawer .flex {
    flex-direction: column;
    gap: 15px;
  }

  .evaluation-drawer .w-62\.5\% {
    width: 100%;
  }

  .evaluation-drawer .w-37\.5\% {
    width: 100%;
  }
}

/* 高度限制样式 */
:deep(.max-h-100px) {
  max-height: 100px;
}

:deep(.max-h-150px) {
  max-height: 150px;
}

:deep(.max-h-200px) {
  max-height: 200px;
}

/* 折叠动画样式 */
:deep(.transform) {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

:deep(.rotate-180) {
  transform: rotate(180deg);
}

/* 文本截断样式 */
:deep(.line-clamp-2) {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 自定义滚动条样式 */
:deep(.overflow-auto::-webkit-scrollbar) {
  width: 6px;
}

:deep(.overflow-auto::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.overflow-auto::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.overflow-auto::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

:deep(.animate-bounce) {
  animation: bounce 1.4s infinite ease-in-out both;
}
</style>
