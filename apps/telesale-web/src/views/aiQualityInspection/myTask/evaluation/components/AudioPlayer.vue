<!--
 * @Date         : 2025-07-11 16:45:00
 * @Description  : 评估模块 - 音频播放器组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="audio-player-container">
    <!-- 检查是否有合并音频URL -->
    <div v-if="conversationData?.mergeAudioURL">
      <!-- 音频检查中（非轮询检查） -->
      <div
        v-if="audioUIState === 'checking'"
        class="flex items-center justify-center p-20px bg-gray-50 rounded-8px"
      >
        <div class="text-center text-gray-500">
          <el-icon size="24" class="mb-10px animate-spin">
            <Stopwatch />
          </el-icon>
          <div class="text-14px">正在检查音频状态...</div>
        </div>
      </div>

      <!-- 音频可播放 -->
      <div v-else-if="audioUIState === 'playable'">
        <Audio
          :audioSrc="conversationData.mergeAudioURL"
          :actionId="conversationData?.id || ''"
          :createdAt="conversationData?.begin"
          :useDirectDownload="true"
        />
      </div>

      <!-- 音频不可播放（合成中） -->
      <div v-else-if="audioUIState === 'synthesizing'" class="relative">
        <!-- 置灰的音频播放器 -->
        <div class="opacity-30 pointer-events-none">
          <Audio
            :audioSrc="conversationData.mergeAudioURL"
            :actionId="conversationData?.id || ''"
            :createdAt="conversationData?.begin"
            :useDirectDownload="true"
          />
        </div>

        <!-- 覆盖层提示 -->
        <div
          class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 rounded-8px"
        >
          <div class="text-center">
            <el-icon
              size="24"
              class="mb-5px text-orange-500"
              :class="{ 'animate-spin': isAudioPolling }"
            >
              <Stopwatch />
            </el-icon>
            <div class="text-red-500 text-12px font-medium synthesis-hint">
              {{
                isAudioPolling
                  ? "合并音频正在生成中，请稍后刷新查看"
                  : "合并音频正在生成中，请稍后刷新查看"
              }}
            </div>
            <div v-if="isAudioPolling" class="mt-5px text-10px text-blue-500">
              音频合并通常需要几分钟时间
            </div>
          </div>
        </div>
      </div>

      <!-- 未检查状态（初始状态） -->
      <div v-else>
        <Audio
          :audioSrc="conversationData.mergeAudioURL"
          :actionId="conversationData?.id || ''"
          :createdAt="conversationData?.begin"
          :useDirectDownload="true"
        />
      </div>
    </div>

    <!-- 当没有合并音频时显示提示信息 -->
    <div
      v-else
      class="flex items-center justify-center p-20px bg-gray-50 rounded-8px"
    >
      <div class="text-center text-gray-500">
        <el-icon
          size="24"
          class="mb-10px"
          :class="{ 'animate-spin': isAnyPolling }"
        >
          <Stopwatch />
        </el-icon>
        <div class="text-14px">
          {{
            isAnyPolling
              ? "合并音频正在生成中，请稍后刷新查看"
              : "合并音频正在生成中，请稍后刷新查看"
          }}
        </div>
        <el-button
          v-if="!isAnyPolling"
          type="primary"
          size="small"
          class="mt-15px"
          :loading="loading"
          @click="handleRefreshAudio"
        >
          刷新音频状态
        </el-button>
        <div v-else class="mt-15px text-12px text-blue-500">
          系统正在自动检查，请稍后
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { Stopwatch } from "@element-plus/icons-vue";
import Audio from "/@/views/AISupport/Quality/DetailDrawer/children/Audio.vue";
import { useEvaluationStore } from "../store";

// 使用统一状态管理
const store = useEvaluationStore();

// 解构状态
const {
  conversationData,
  audioUIState,
  isAudioPolling,
  isDataPolling,
  loading
} = store;

/**
 * 是否有任何轮询在进行
 */
const isAnyPolling = computed(() => {
  return isDataPolling.value || isAudioPolling.value;
});

// 定义事件
const emit = defineEmits<{
  refreshAudio: [];
}>();

/**
 * 处理刷新音频
 */
function handleRefreshAudio() {
  emit("refreshAudio");
}
</script>

<style scoped>
.synthesis-hint {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
