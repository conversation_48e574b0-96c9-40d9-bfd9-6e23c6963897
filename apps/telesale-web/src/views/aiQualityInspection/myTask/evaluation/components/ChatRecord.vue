<!--
 * @Date         : 2025-07-11 16:50:00
 * @Description  : 评估模块 - 对话记录组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div
    class="w-62.5% flex flex-col bg-white rounded-8px shadow-sm overflow-hidden"
  >
    <div class="p-15px border-b border-gray-200">
      <h3 class="text-16px font-bold text-blue-500">通话记录</h3>
    </div>

    <!-- 音频播放器区域 - 仅在非纯文本模式下显示 -->
    <div v-if="!isTextOnlyMode" class="p-15px border-b border-gray-200">
      <AudioPlayer @refresh-audio="handleRefreshAudio" />
    </div>

    <!-- 对话内容区域 -->
    <div class="flex-1 p-20px overflow-auto bg-gray-50" ref="chatContentRef">
      <!-- 消息列表 -->
      <div class="space-y-15px">
        <div
          v-for="(message, index) in conversationData?.messages"
          :key="index"
          class="flex flex-col gap-5px"
        >
          <!-- 时间戳显示 -->
          <div v-if="message.start" class="text-center text-gray-400 text-12px">
            {{ formatTimestamp(message.start) }}
          </div>

          <!-- 消息内容 -->
          <div
            class="flex"
            :class="message.role === 'bot' ? 'justify-start' : 'justify-end'"
          >
            <!-- 机器人消息 -->
            <div
              v-if="message.role === 'bot'"
              class="flex items-start gap-10px max-w-80%"
            >
              <!-- 机器人头像 -->
              <div
                class="w-40px h-40px rounded-full bg-blue-500 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
              >
                {{ courseData?.course?.course?.botName?.charAt(0) || "机" }}
              </div>

              <!-- 消息内容 -->
              <div class="flex flex-col gap-8px">
                <div
                  class="bg-white p-15px rounded-12px shadow-sm border border-gray-200 text-gray-800 leading-relaxed"
                >
                  {{ message.content }}
                </div>

                <!-- 音频播放控制 - 仅在非纯文本模式下显示 -->
                <div
                  v-if="!isTextOnlyMode && message.key"
                  class="flex items-center gap-8px"
                >
                  <button
                    @click="playAudio(message)"
                    class="flex items-center gap-5px px-10px py-5px bg-blue-50 hover:bg-blue-100 rounded-6px text-blue-600 text-12px transition-colors"
                  >
                    <el-icon size="14">
                      <VideoPlay v-if="playingMessage !== message" />
                      <VideoPause v-else />
                    </el-icon>
                    <span>
                      {{ playingMessage === message ? "暂停" : "播放" }}
                    </span>
                  </button>

                  <!-- 播放状态指示 -->
                  <div
                    v-if="playingMessage === message"
                    class="flex items-center gap-5px text-blue-600 text-12px"
                  >
                    <div class="flex gap-1">
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 0ms"
                      />
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 150ms"
                      />
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 300ms"
                      />
                    </div>
                    <span>播放中</span>
                  </div>
                </div>

                <!-- 无音频提示 - 仅在非纯文本模式下显示 -->
                <div
                  v-if="!isTextOnlyMode && !message.key"
                  class="text-gray-400 text-12px"
                >
                  暂无音频
                </div>
              </div>
            </div>

            <!-- 用户消息 -->
            <div v-else class="flex items-start gap-10px max-w-80%">
              <!-- 消息内容和音频控制 -->
              <div class="flex flex-col gap-8px">
                <!-- 消息内容 -->
                <div
                  class="bg-blue-500 text-white p-15px rounded-12px shadow-sm leading-relaxed"
                >
                  {{ message.content }}
                </div>

                <!-- 音频播放控制 - 仅在非纯文本模式下显示 -->
                <div
                  v-if="!isTextOnlyMode && message.key"
                  class="flex items-center justify-end gap-8px"
                >
                  <button
                    @click="playAudio(message)"
                    class="flex items-center gap-5px px-10px py-5px bg-blue-50 hover:bg-blue-100 rounded-6px text-blue-600 text-12px transition-colors"
                  >
                    <el-icon size="14">
                      <VideoPlay v-if="playingMessage !== message" />
                      <VideoPause v-else />
                    </el-icon>
                    <span>
                      {{ playingMessage === message ? "暂停" : "播放" }}
                    </span>
                  </button>

                  <!-- 播放状态指示 -->
                  <div
                    v-if="playingMessage === message"
                    class="flex items-center gap-5px text-blue-600 text-12px"
                  >
                    <div class="flex gap-1">
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 0ms"
                      />
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 150ms"
                      />
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 300ms"
                      />
                    </div>
                    <span>播放中</span>
                  </div>
                </div>

                <!-- 无音频提示 - 仅在非纯文本模式下显示 -->
                <div
                  v-if="!isTextOnlyMode && !message.key"
                  class="text-gray-400 text-12px text-right"
                >
                  暂无音频
                </div>
              </div>

              <!-- 用户头像 -->
              <div
                class="w-40px h-40px rounded-full bg-gray-400 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
              >
                我
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的音频播放器 - 仅在非纯文本模式下渲染 -->
    <audio
      v-if="!isTextOnlyMode"
      ref="audioPlayer"
      style="display: none"
      @ended="handleAudioEnded"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { VideoPlay, VideoPause } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import AudioPlayer from "./AudioPlayer.vue";
import { useEvaluationStore, type ExtendedPracticeRecord } from "../store";
import type { WorkerTrainingTaskMessage } from "/@/api/AIQualityInspection/taskManagement";

// 使用统一状态管理
const store = useEvaluationStore();

// 解构状态
const { conversationData, courseData, isTextOnlyMode } = store;

// 本地状态
const playingMessage = ref<WorkerTrainingTaskMessage | null>(null);
const audioPlayer = ref<HTMLAudioElement | null>(null);
const chatContentRef = ref<HTMLElement>();

// 定义事件
const emit = defineEmits<{
  refreshAudio: [];
  practiceRecordChange: [record: ExtendedPracticeRecord];
}>();

/**
 * 处理刷新音频
 */
function handleRefreshAudio() {
  emit("refreshAudio");
}

/**
 * 格式化时间戳为可读时间
 * @param timestamp 秒级时间戳字符串
 * @returns 格式化的时间字符串 YYYY-MM-DD HH:mm:ss
 */
function formatTimestamp(timestamp?: string): string {
  if (!timestamp) return "";

  const date = new Date(Number(timestamp) * 1000);

  // 使用dayjs格式化为YYYY-MM-DD HH:mm:ss格式
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
}

/**
 * 播放音频
 * @param message 消息对象
 */
function playAudio(message: WorkerTrainingTaskMessage) {
  // 在纯文本模式下禁用音频播放
  if (isTextOnlyMode.value) {
    ElMessage.warning("当前为纯文本模式，无法播放音频");
    return;
  }

  if (playingMessage.value === message) {
    // 如果当前正在播放，则停止
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      audioPlayer.value.currentTime = 0;
    }
    playingMessage.value = null;
  } else {
    // 开始播放新的音频
    playingMessage.value = message;

    // 设置真实的音频URL
    if (audioPlayer.value && message.key) {
      // 根据消息角色选择音频格式：bot使用mp3，user使用wav
      const audioFormat = message.role === "bot" ? "mp3" : "wav";
      audioPlayer.value.src = `https://wuhan-file.tos-cn-beijing.volces.com/telesalerobot/${message.key}.${audioFormat}`;
      audioPlayer.value.play().catch(error => {
        console.error("播放音频失败:", error);
        ElMessage.error("音频播放失败");
        playingMessage.value = null;
      });
    } else {
      console.warn("消息缺少音频key，无法播放音频");
      ElMessage.warning("该消息没有音频资源");
    }
  }
}

/**
 * 音频播放结束处理
 */
function handleAudioEnded() {
  playingMessage.value = null;
}

/**
 * 滚动到对话开始位置
 */
function scrollToTop() {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = 0;
    }
  });
}

// 暴露方法给父组件
defineExpose({
  scrollToTop
});
</script>

<style scoped>
/* 对话记录样式 */
.chat-content {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.chat-content::-webkit-scrollbar {
  width: 6px;
}

.chat-content::-webkit-scrollbar-track {
  background: #f7fafc;
}

.chat-content::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 练习记录选择器高亮样式 */
:deep(.selected-practice-record) {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  font-weight: 500;
}

:deep(.selected-practice-record:hover) {
  background-color: #bbdefb !important;
}

/* 选中项的标签样式优化 */
:deep(.selected-practice-record .el-tag) {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}
</style>
