<!--
 * @Date         : 2025-07-11 16:55:00
 * @Description  : 评估模块 - 评估结果组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="w-37.5% flex flex-col h-full">
    <!-- 右侧内容滚动容器 -->
    <div class="flex-1 overflow-auto pr-5px">
      <!-- 对话评估报告 -->
      <el-card class="mb-20px">
        <template #header>
          <div class="flex justify-between items-center">
            <!-- 左侧：标题和练习记录选择器 -->
            <div class="flex items-center gap-15px">
              <span class="text-16px font-bold">对话评估报告</span>

              <!-- 练习记录选择器（仅练习模式显示） -->
              <div
                v-if="isPracticeMode && practiceRecords.length > 0"
                class="flex items-center gap-10px"
              >
                <el-select
                  v-model="selectedPracticeRecord"
                  placeholder="选择练习记录"
                  style="width: 180px"
                  value-key="id"
                  filterable
                  @change="handlePracticeRecordChange"
                >
                  <el-option
                    v-for="record in practiceRecords"
                    :key="record.id"
                    :value="record"
                    :label="`第${record.practiceCount}次练习`"
                  >
                    <div class="flex flex-col py-2px">
                      <div
                        class="text-14px font-medium flex items-center justify-between"
                      >
                        <span>
                          第{{ record.practiceCount }}次练习（{{
                            record.score || "未评级"
                          }}级）
                        </span>
                        <el-tag
                          v-if="
                            record.score && record.id === highestScoreRecordId
                          "
                          :type="getScoreTagType(record.score)"
                          size="small"
                        >
                          最高分
                        </el-tag>
                      </div>
                      <div class="text-12px text-gray-500 mt-2px">
                        {{ formatPracticeTime(record.finishedAt) }}
                      </div>
                    </div>
                  </el-option>
                </el-select>

                <!-- 开始第n次练习按钮 - 管理员视角时隐藏 -->
                <el-button
                  v-if="!props.isAdminView"
                  type="primary"
                  @click="handleStartPractice"
                >
                  {{ getPracticeButtonText() }}
                </el-button>
              </div>
            </div>

            <!-- 右侧：收起按钮 -->
            <el-icon
              class="cursor-pointer"
              :class="{ 'transform rotate-180': !evaluationCollapsed }"
              @click="toggleEvaluationCollapse"
            >
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <div v-show="!evaluationCollapsed" class="p-10px">
          <div class="text-center mb-20px">
            <div
              class="text-24px font-bold mb-10px flex items-center justify-center gap-8px"
              :class="{
                'text-green-500': currentEvaluationLevel === 'A',
                'text-yellow-500': currentEvaluationLevel === 'B',
                'text-orange-500': currentEvaluationLevel === 'C',
                'text-red-500': currentEvaluationLevel === 'D'
              }"
            >
              <span>任务得分：{{ getDisplayScore() }}</span>
              <!-- 轮询时显示loading图标 -->
              <el-icon
                v-if="shouldShowLoading()"
                size="16"
                class="animate-spin text-blue-500"
              >
                <Stopwatch />
              </el-icon>
            </div>

            <!-- 轮询状态提示 -->
            <div
              v-if="shouldShowLoading()"
              class="flex flex-col items-center justify-center gap-8px text-blue-500 text-14px"
            >
              <div class="flex items-center gap-8px">
                <el-icon size="16" class="animate-spin">
                  <Stopwatch />
                </el-icon>
                <span>正在获取最新评估结果...</span>
              </div>
              <div class="text-12px text-gray-500 text-center">
                可关闭页面，稍后查看任务结果～
              </div>
            </div>
          </div>

          <!-- 评估结果 -->
          <div v-if="evaluationResult" class="mb-15px">
            <div class="text-16px font-bold mb-10px">评估结果：</div>
            <div
              class="text-14px text-gray-700 leading-relaxed overflow-auto"
              v-html="evaluationResult"
            />
          </div>

          <!-- 评估原因 -->
          <div v-if="evaluationReason" class="mb-15px">
            <div class="text-16px font-bold mb-10px">评估原因：</div>
            <div
              class="text-14px text-gray-700 leading-relaxed max-h-200px overflow-auto"
              v-html="evaluationReason"
            />
          </div>

          <!-- 优势点 -->
          <div v-if="evaluationStrengths.length > 0" class="mb-15px">
            <div class="text-16px font-bold mb-10px">优势点：</div>
            <ul class="pl-20px max-h-150px overflow-auto">
              <li
                v-for="(item, index) in evaluationStrengths"
                :key="index"
                class="text-14px mb-5px"
              >
                {{ item }}
              </li>
            </ul>
          </div>

          <!-- 改进建议 -->
          <div v-if="evaluationImprovements.length > 0">
            <div class="text-16px font-bold mb-10px">改进建议：</div>
            <ul class="pl-20px max-h-150px overflow-auto">
              <li
                v-for="(item, index) in evaluationImprovements"
                :key="index"
                class="text-14px mb-5px"
              >
                {{ item }}
              </li>
            </ul>
          </div>
        </div>
      </el-card>

      <!-- 任务信息卡片 -->
      <el-card class="mb-20px">
        <template #header>
          <div
            class="flex justify-between items-center cursor-pointer"
            @click="toggleTaskInfoCollapse"
          >
            <span class="text-16px font-bold">任务信息</span>
            <el-icon :class="{ 'transform rotate-180': !taskInfoCollapsed }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <div v-show="!taskInfoCollapsed" class="p-10px">
          <!-- 任务模式显示 -->
          <div class="mb-15px">
            <div class="flex items-center gap-10px">
              <span class="text-14px font-medium">任务模式：</span>
              <el-tag :type="taskModeType">{{ taskModeText }}</el-tag>
            </div>
          </div>

          <el-descriptions :column="1">
            <el-descriptions-item label="开始时间">
              {{
                conversationData?.begin
                  ? dayjs
                      .unix(Number(conversationData.begin))
                      .format("YYYY-MM-DD HH:mm:ss")
                  : "未知"
              }}
            </el-descriptions-item>

            <el-descriptions-item label="结束时间">
              {{
                conversationData?.end
                  ? dayjs
                      .unix(Number(conversationData.end))
                      .format("YYYY-MM-DD HH:mm:ss")
                  : "未知"
              }}
            </el-descriptions-item>

            <el-descriptions-item label="任务持续时长">
              {{ formatTime(conversationData?.duration || 0) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 课程信息卡片 -->
      <el-card class="mb-20px">
        <template #header>
          <div
            class="flex justify-between items-center cursor-pointer"
            @click="toggleCourseInfoCollapse"
          >
            <span class="text-16px font-bold">课程信息</span>
            <el-icon :class="{ 'transform rotate-180': !courseInfoCollapsed }">
              <ArrowDown />
            </el-icon>
          </div>
        </template>

        <div v-show="!courseInfoCollapsed" class="p-10px">
          <el-descriptions :column="1">
            <el-descriptions-item label="课程名称">
              {{ courseData?.course?.course?.name || "未命名课程" }}
            </el-descriptions-item>

            <el-descriptions-item label="最大持续时间">
              {{ courseData?.course?.course?.maxDuration || 60 }} 分钟
            </el-descriptions-item>

            <el-descriptions-item label="机器人名称">
              {{ courseData?.course?.course?.botName || "未设置" }}
            </el-descriptions-item>

            <el-descriptions-item label="机器人角色">
              {{ courseData?.course?.course?.botRole || "未设置" }}
            </el-descriptions-item>

            <el-descriptions-item label="练习目标">
              <div
                class="whitespace-pre-wrap break-words text-12px max-h-100px overflow-auto"
              >
                {{ courseData?.course?.course?.target || "无练习目标" }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="创建时间">
              {{
                courseData?.course?.course?.createdAt
                  ? new Date(
                      courseData.course.course.createdAt
                    ).toLocaleString()
                  : "未知"
              }}
            </el-descriptions-item>

            <el-descriptions-item label="用户标签">
              <div
                v-loading="tagsLoading"
                class="whitespace-pre-wrap break-words"
              >
                <el-tag
                  v-for="tag in userTags"
                  :key="tag.id"
                  effect="plain"
                  class="mr-5px mb-5px"
                >
                  {{ tag.name }}
                </el-tag>
                <span
                  v-if="userTags.length === 0 && !tagsLoading"
                  class="text-gray-400"
                >
                  暂无标签
                </span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { Stopwatch, ArrowDown } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { useEvaluationStore, type ExtendedPracticeRecord } from "../store";
import { TASK_MODE } from "/@/api/AIQualityInspection/taskManagement";

// 定义props
const props = defineProps<{
  /** 是否为管理员视角 */
  isAdminView?: boolean;
}>();

// 使用统一状态管理
const store = useEvaluationStore();

// 解构状态
const {
  currentEvaluationLevel,
  evaluationResult,
  evaluationReason,
  evaluationStrengths,
  evaluationImprovements,
  isPracticeMode,
  isDataPolling,
  userTags,
  tagsLoading,
  conversationData,
  courseData,
  taskMode,
  practiceRecords,
  selectedPracticeRecord
} = store;

// 本地状态 - 折叠状态
const evaluationCollapsed = ref(false);
const taskInfoCollapsed = ref(true); // 默认折叠
const courseInfoCollapsed = ref(true); // 默认折叠

// 定义事件
const emit = defineEmits<{
  practiceRecordChange: [record: ExtendedPracticeRecord];
  startPractice: [];
}>();

/**
 * 获取任务模式文本
 */
const taskModeText = computed(() => {
  switch (taskMode.value) {
    case TASK_MODE.PRACTICE:
      return "练习模式";
    case TASK_MODE.EXAM:
      return "考试模式";
    default:
      return "考试模式"; // 历史数据默认为考试模式
  }
});

/**
 * 获取任务模式标签类型
 */
const taskModeType = computed(() => {
  switch (taskMode.value) {
    case TASK_MODE.PRACTICE:
      return "success";
    case TASK_MODE.EXAM:
      return "warning";
    default:
      return "warning"; // 历史数据默认为考试模式
  }
});

/**
 * 获取最高分记录ID
 * 按照 ABCD 评级排序（A 为最高），相同级别取时间最近的一条
 */
const highestScoreRecordId = computed(() => {
  if (!practiceRecords.value || practiceRecords.value.length === 0) {
    return null;
  }

  // 定义评级权重，A 为最高
  const scoreWeight = {
    A: 4,
    B: 3,
    C: 2,
    D: 1
  };

  // 找到最高分记录
  const highestRecord = practiceRecords.value.reduce((highest, current) => {
    const currentScore = current.score || "";
    const highestScore = highest.score || "";

    // 如果当前记录没有评分，跳过
    if (!currentScore) return highest;

    // 如果最高记录没有评分，当前记录成为最高
    if (!highestScore) return current;

    const currentWeight = scoreWeight[currentScore] || 0;
    const highestWeight = scoreWeight[highestScore] || 0;

    // 比较评级权重
    if (currentWeight > highestWeight) {
      return current;
    } else if (currentWeight === highestWeight) {
      // 相同评级，取时间最近的
      const currentTime = new Date(current.finishedAt).getTime();
      const highestTime = new Date(highest.finishedAt).getTime();
      return currentTime > highestTime ? current : highest;
    } else {
      return highest;
    }
  });

  return highestRecord?.id || null;
});

/**
 * 获取显示的得分文本
 */
function getDisplayScore(): string {
  // 如果正在轮询且没有得分，显示"评估中..."
  if (shouldShowLoading()) {
    return "评估中...";
  }

  // 否则显示当前评级或"未评分"
  return currentEvaluationLevel.value || "未评分";
}

/**
 * 判断是否应该显示loading图标
 */
function shouldShowLoading(): boolean {
  // 正在轮询且没有有效的评级时显示loading
  // 只使用 isDataPolling，避免 pollingLoading 的频繁变化导致闪烁
  const isPolling = isDataPolling.value;
  const hasValidScore = currentEvaluationLevel.value !== null;

  return isPolling && !hasValidScore;
}

/**
 * 切换评估报告折叠状态
 */
function toggleEvaluationCollapse() {
  evaluationCollapsed.value = !evaluationCollapsed.value;
}

/**
 * 切换任务信息折叠状态
 */
function toggleTaskInfoCollapse() {
  taskInfoCollapsed.value = !taskInfoCollapsed.value;
}

/**
 * 切换课程信息折叠状态
 */
function toggleCourseInfoCollapse() {
  courseInfoCollapsed.value = !courseInfoCollapsed.value;
}

/**
 * 处理练习记录选择变化
 */
function handlePracticeRecordChange(record: ExtendedPracticeRecord) {
  console.log("🔄 练习记录选择变化:", {
    recordId: record?.id,
    practiceCount: record?.practiceCount,
    score: record?.score
  });
  emit("practiceRecordChange", record);
}

/**
 * 获取评分标签类型
 */
function getScoreTagType(
  score: string
): "success" | "warning" | "info" | "danger" {
  switch (score) {
    case "A":
      return "success";
    case "B":
      return "info";
    case "C":
      return "warning";
    case "D":
      return "danger";
    default:
      return "info";
  }
}

/**
 * 格式化时间显示
 * @param seconds 秒数
 * @returns 格式化的时间字符串 HH:mm:ss
 */
function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return [
    hours.toString().padStart(2, "0"),
    minutes.toString().padStart(2, "0"),
    secs.toString().padStart(2, "0")
  ].join(":");
}

/**
 * 格式化练习时间
 */
function formatPracticeTime(timeStr: string): string {
  return dayjs(timeStr).format("YYYY-MM-DD HH:mm:ss");
}

/**
 * 获取练习按钮文案
 */
function getPracticeButtonText(): string {
  if (!isPracticeMode.value) {
    return "开始练习";
  }

  // 使用practiceRecords来判断练习次数
  const practiceCount = practiceRecords.value.length || 0;
  return `开始第${practiceCount + 1}次练习`;
}

/**
 * 处理开始练习
 */
function handleStartPractice() {
  emit("startPractice");
}
</script>

<style scoped>
/* 评估结果样式 */
.evaluation-result {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.evaluation-result::-webkit-scrollbar {
  width: 6px;
}

.evaluation-result::-webkit-scrollbar-track {
  background: #f7fafc;
}

.evaluation-result::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.evaluation-result::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 确保选择器样式正常 */
:deep(.el-select) {
  .el-input__wrapper {
    transition: all 0.3s ease;
  }

  .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-select-dropdown) {
  .el-select-dropdown__item {
    padding: 8px 12px;
  }

  .el-select-dropdown__item.selected {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    font-weight: 500;
  }
}
</style>
