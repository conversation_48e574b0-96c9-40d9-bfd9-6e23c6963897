# 评估模块重构说明

## 🔄 功能恢复更新

### 恢复的原版功能
基于old.vue文件，已恢复以下原版功能和样式：

1. **对话记录样式恢复**
   - 恢复原版的对话气泡样式和布局
   - 恢复机器人和用户头像显示
   - 恢复时间戳显示格式
   - 恢复单条消息音频播放功能

2. **评估结果详细展示**
   - 恢复完整的评估报告卡片布局
   - 恢复练习记录选择器（位于评估报告头部）
   - 恢复任务信息和课程信息折叠卡片
   - 恢复优势点和改进建议展示

3. **音频播放功能**
   - 恢复原版Audio组件引用
   - 恢复音频状态检查和轮询提示
   - 恢复音频合成中的覆盖层提示

4. **交互逻辑恢复**
   - 恢复折叠/展开功能
   - 恢复练习记录切换逻辑
   - 恢复音频播放控制

### 保持的重构结构
- 保持组件化的文件结构
- 保持VueUse状态管理
- 保持业务逻辑hooks抽取
- 保持TypeScript类型支持

## 📁 目录结构

```
evaluation/
├── index.vue                    # 主入口组件
├── store.ts                     # 统一状态管理
├── README.md                    # 说明文档
├── components/                  # 子组件目录
│   ├── AudioPlayer.vue         # 音频播放器组件
│   ├── ChatRecord.vue          # 对话记录组件
│   └── EvaluationResult.vue    # 评估结果组件
└── hooks/                       # 业务逻辑hooks
    ├── index.ts                # hooks入口文件
    ├── useEvaluationData.ts    # 评估数据管理
    └── useAudioManager.ts      # 音频管理
```

## 🔧 重构内容

### 1. 状态管理重构
- **store.ts**: 使用VueUse的createGlobalState创建统一状态管理
- 集中管理所有状态：课程数据、对话数据、评估结果、音频状态、轮询状态等
- 提供计算属性和通用方法

### 2. 组件拆分
- **AudioPlayer.vue**: 音频播放器组件，处理音频播放、状态检查、合成提示等
- **ChatRecord.vue**: 对话记录组件，显示对话内容、练习记录选择器等
- **EvaluationResult.vue**: 评估结果组件，显示评级、评估详情、用户标签等

### 3. 业务逻辑抽取
- **useEvaluationData.ts**: 评估数据相关的业务逻辑
  - 获取任务信息和练习记录
  - 获取历史练习数据
  - 获取课程详情
  - 获取用户标签
- **useAudioManager.ts**: 音频相关的业务逻辑
  - 音频可播放性检查
  - 音频数据刷新
  - 轮询管理（数据轮询、音频轮询）

## 🎯 重构优势

### 1. 可维护性提升
- 代码结构清晰，职责分离
- 组件粒度合适，便于复用和测试
- 业务逻辑集中管理，便于维护

### 2. 性能优化
- 状态管理优化，减少不必要的响应式更新
- 组件按需加载，提升首屏性能
- 轮询逻辑优化，避免内存泄漏

### 3. 开发体验
- TypeScript类型支持完善
- 组件接口清晰，便于协作开发
- 代码复用性高，减少重复代码

## 📋 API接口说明

### 主要接口
- `getWorkerTrainingTaskCourseInfo`: 获取课程详情（已添加workerTrainingConversationId参数）
- `getWorkerTrainingTaskInfo`: 获取任务信息
- `getWorkerTrainingTaskCourseHistoricalData`: 获取历史练习数据
- `getMget`: 获取用户标签

### 接口参数更新
所有调用`getWorkerTrainingTaskCourseInfo`的地方都已添加`workerTrainingConversationId`参数：
- 优先使用历史记录中的对话ID
- 其次使用props中的对话ID
- 最后使用当前对话数据的ID

## 🔄 轮询机制

### 数据轮询
- 检查任务得分和音频URL是否完成
- 1秒间隔轮询
- 自动停止条件：得分和音频都已完成

### 音频轮询
- 检查音频文件可播放性
- 2秒间隔轮询
- 自动停止条件：音频可播放

## 🎨 样式说明

### 响应式设计
- 大屏幕：左右布局（62.5% + 37.5%）
- 小屏幕：上下布局（100% + 100%）

### 主题色彩
- 蓝色：通话记录标题
- 绿色：评估结果标题
- 灰色：辅助信息和占位内容

## 🚀 使用方式

```vue
<template>
  <EvaluationDrawer
    v-model:visible="evaluationVisible"
    :course-info="currentCourse"
    @close="handleEvaluationClose"
  />
</template>

<script setup>
import EvaluationDrawer from './evaluation/index.vue';

const evaluationVisible = ref(false);
const currentCourse = ref(null);

function handleEvaluationClose() {
  console.log('评估抽屉已关闭');
}
</script>
```

## 🔧 开发注意事项

1. **状态管理**: 优先使用store中的状态，避免在组件中重复定义
2. **事件传递**: 使用emit传递事件，保持组件间的松耦合
3. **错误处理**: 统一使用ElMessage显示错误信息
4. **类型安全**: 充分利用TypeScript类型检查
5. **性能优化**: 注意轮询的启动和停止，避免内存泄漏
