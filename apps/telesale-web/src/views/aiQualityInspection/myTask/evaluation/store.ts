/**
 * @Date         : 2025-07-11 16:30:00
 * @Description  : 评估模块统一状态管理 - 使用VueUse
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, computed } from "vue";
import { createGlobalState } from "@vueuse/core";
import {
  WorkerTrainingTaskCourseInstance,
  WorkerTrainingConversationInstance,
  PracticeRecord,
  TASK_MODE,
  WorkerTrainingTaskCourseItem
} from "/@/api/AIQualityInspection/taskManagement";
import { TagInstance } from "/@/api/AIQualityInspection/excellentCases";

/**
 * 扩展的练习记录类型，包含历史数据项
 */
export interface ExtendedPracticeRecord extends PracticeRecord {
  historicalItem?: WorkerTrainingTaskCourseItem;
  duration?: number;
}

/**
 * 音频UI状态枚举
 */
export enum AudioUIState {
  CHECKING = 'checking',
  PLAYABLE = 'playable',
  SYNTHESIZING = 'synthesizing',
  UNCHECKED = 'unchecked'
}

/**
 * 评估模块统一状态管理
 */
export const useEvaluationStore = createGlobalState(() => {
  // ===== 基础状态 =====
  const loading = ref<boolean>(false);
  const drawerVisible = ref<boolean>(false);

  // ===== 课程和对话数据 =====
  const courseData = ref<WorkerTrainingTaskCourseInstance | null>(null);
  const conversationData = ref<WorkerTrainingConversationInstance | null>(null);

  // ===== 评估相关状态 =====
  const evaluationLevel = ref<"A" | "B" | "C" | "D" | null>(null);
  const evaluationResult = ref<string>("");
  const evaluationReason = ref<string>("");
  const evaluationStrengths = ref<string[]>([]);
  const evaluationImprovements = ref<string[]>([]);

  // ===== 任务模式相关状态 =====
  const taskMode = ref<string>("");
  const taskName = ref<string>("");
  const practiceRecords = ref<ExtendedPracticeRecord[]>([]);
  const selectedPracticeRecord = ref<ExtendedPracticeRecord | null>(null);

  // ===== 历史练习数据状态 =====
  const historicalData = ref<WorkerTrainingTaskCourseItem[]>([]);
  const historicalDataLoading = ref(false);

  // ===== 音频相关状态 =====
  const audioPlayable = ref<boolean | null>(null);
  const audioCheckLoading = ref<boolean>(false);

  // ===== 轮询相关状态 =====
  const dataPollingTimer = ref<NodeJS.Timeout | null>(null);
  const isDataPolling = ref(false);
  const pollingLoading = ref(false); // 轮询时的loading状态
  const audioPollingTimer = ref<NodeJS.Timeout | null>(null);
  const isAudioPolling = ref(false);

  // ===== 用户标签 =====
  const userTags = ref<TagInstance[]>([]);
  const tagsLoading = ref<boolean>(false);

  // ===== 计算属性 =====

  /**
   * 是否为练习模式
   */
  const isPracticeMode = computed(() => taskMode.value === TASK_MODE.PRACTICE);

  /**
   * 是否为纯文本模式
   */
  const isTextOnlyMode = computed(() => {
    return conversationData.value?.trainingMode === "text";
  });

  /**
   * 音频UI状态
   */
  const audioUIState = computed((): AudioUIState => {
    if (audioCheckLoading.value && !isAudioPolling.value) {
      return AudioUIState.CHECKING;
    }
    if (audioPlayable.value === true) {
      return AudioUIState.PLAYABLE;
    }
    if (audioPlayable.value === false) {
      return AudioUIState.SYNTHESIZING;
    }
    return AudioUIState.UNCHECKED;
  });

  /**
   * 获取当前显示的评级
   * 练习模式下优先显示选中练习记录的评级，否则显示默认评级
   */
  const currentEvaluationLevel = computed(() => {
    if (isPracticeMode.value && selectedPracticeRecord.value) {
      const score = selectedPracticeRecord.value.score;
      // 只有当得分是有效的评级（A、B、C、D）时才返回，否则返回null
      if (score && score !== "未评分" && score !== "" && ["A", "B", "C", "D"].includes(score)) {
        return score as "A" | "B" | "C" | "D";
      }
      return null;
    }
    return evaluationLevel.value;
  });

  /**
   * 获取当前显示的练习时间
   */
  const currentPracticeTime = computed(() => {
    if (isPracticeMode.value && selectedPracticeRecord.value?.finishedAt) {
      return selectedPracticeRecord.value.finishedAt;
    }
    return conversationData.value?.end || "";
  });

  /**
   * 是否有任何轮询在进行
   */
  const isAnyPolling = computed(() => {
    return isDataPolling.value || isAudioPolling.value;
  });

  // ===== 方法 =====

  /**
   * 重置所有状态
   */
  function resetState() {
    loading.value = false;
    courseData.value = null;
    conversationData.value = null;
    evaluationLevel.value = null;
    evaluationResult.value = "";
    evaluationReason.value = "";
    evaluationStrengths.value = [];
    evaluationImprovements.value = [];
    taskMode.value = "";
    taskName.value = "";
    practiceRecords.value = [];
    selectedPracticeRecord.value = null;
    historicalData.value = [];
    historicalDataLoading.value = false;
    audioPlayable.value = null;
    audioCheckLoading.value = false;
    pollingLoading.value = false;
    userTags.value = [];
    tagsLoading.value = false;
    stopAllPolling();
  }

  /**
   * 停止所有轮询
   */
  function stopAllPolling() {
    stopDataPolling();
    stopAudioPolling();
  }

  /**
   * 停止数据轮询
   */
  function stopDataPolling() {
    if (dataPollingTimer.value) {
      clearInterval(dataPollingTimer.value);
      dataPollingTimer.value = null;
    }
    isDataPolling.value = false;
  }

  /**
   * 停止音频轮询
   */
  function stopAudioPolling() {
    if (audioPollingTimer.value) {
      clearInterval(audioPollingTimer.value);
      audioPollingTimer.value = null;
    }
    isAudioPolling.value = false;
  }

  /**
   * 设置课程信息
   */
  function setCourseInfo(courseInfo: WorkerTrainingTaskCourseInstance | null) {
    courseData.value = courseInfo;
  }

  /**
   * 设置对话数据
   */
  function setConversationData(conversation: WorkerTrainingConversationInstance | null) {
    conversationData.value = conversation;
  }

  /**
   * 更新评估信息
   */
  function updateEvaluationInfo(appraise: any) {
    if (appraise) {
      evaluationLevel.value = (appraise.score as "A" | "B" | "C" | "D") || null;
      evaluationResult.value = appraise.result || "";
      evaluationReason.value = appraise.reason || "";
      evaluationStrengths.value = [];
      evaluationImprovements.value = [];
    }
  }

  return {
    // 状态
    loading,
    drawerVisible,
    courseData,
    conversationData,
    evaluationLevel,
    evaluationResult,
    evaluationReason,
    evaluationStrengths,
    evaluationImprovements,
    taskMode,
    taskName,
    practiceRecords,
    selectedPracticeRecord,
    historicalData,
    historicalDataLoading,
    audioPlayable,
    audioCheckLoading,
    dataPollingTimer,
    isDataPolling,
    pollingLoading,
    audioPollingTimer,
    isAudioPolling,
    userTags,
    tagsLoading,

    // 计算属性
    isPracticeMode,
    isTextOnlyMode,
    audioUIState,
    currentEvaluationLevel,
    currentPracticeTime,
    isAnyPolling,

    // 方法
    resetState,
    stopAllPolling,
    stopDataPolling,
    stopAudioPolling,
    setCourseInfo,
    setConversationData,
    updateEvaluationInfo
  };
});
