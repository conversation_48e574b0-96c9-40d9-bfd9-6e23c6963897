<!--
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 课程练习抽屉 - 重构版本（使用hooks）
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="drawerVisible"
    title="课程练习"
    :close-on-press-escape="false"
    :show-close="false"
    :close-on-click-modal="false"
    size="90%"
    destroy-on-close
    direction="rtl"
    :before-close="handleClose"
    @open="handleDrawerOpen"
  >
    <div class="exercise-drawer" v-loading="store.loading.value">
      <!-- 课程信息为空时的提示 -->
      <div
        v-if="!store.courseInfo.value"
        class="flex items-center justify-center h-full"
      >
        <el-empty description="课程信息加载中..." />
      </div>

      <!-- 练习内容区域 -->
      <div v-else class="flex gap-20px h-full relative">
        <!-- 左侧信息区域 -->
        <InfoPanel />

        <!-- 右侧对话区域 -->
        <div
          class="flex-1 flex flex-col bg-white rounded-8px shadow-sm overflow-hidden"
        >
          <!-- 对话区域顶部 - 计时器 -->
          <div class="p-15px border-b border-gray-200 flex justify-end">
            <div
              class="timer-box flex items-center gap-10px bg-blue-50 px-15px py-5px rounded-20px"
            >
              <el-icon><Stopwatch /></el-icon>
              <span>{{ cleanFormattedTime }}</span>
            </div>
          </div>

          <!-- 对话内容区域 -->
          <ChatArea
            ref="chatAreaRef"
            :is-message-stream-playing="getIsMessageStreamPlaying"
            :should-disable-play-button="getShouldDisablePlayButton"
            :complete-stream-audio-playback="getCompleteStreamAudioPlayback"
          />

          <!-- 录音控制区域 -->
          <RecordingControl
            @abandon="handleAbandonPractice"
            @finish="handleFinishPractice"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  ref,
  defineModel,
  onMounted,
  onBeforeUnmount,
  nextTick,
  watch,
  computed
} from "vue";
import { Stopwatch } from "@element-plus/icons-vue";

// 导入拆分的组件
import InfoPanel from "./components/InfoPanel.vue";
import ChatArea from "./components/ChatArea.vue";
import RecordingControl from "./components/RecordingControl.vue";

// 导入统一状态管理
import { useExerciseStore } from "./store";

// 开发环境下的调试工具已移除
if (import.meta.env.DEV) {
  // 添加loading状态调试工具
  setTimeout(() => {
    (window as any).exerciseDebug = {
      ...(window as any).exerciseDebug,
      clearLoading: () => {
        store.forceClearLoading();
        console.log("🔧 通过调试工具清除loading状态");
      },
      checkLoadingState: () => {
        console.log("🔍 当前loading状态:", {
          loading: store.loading.value,
          isPracticing: store.isPracticing.value,
          isTimerRunning: store.isTimerRunning.value,
          drawerVisible: drawerVisible.value,
          timestamp: new Date().toLocaleTimeString()
        });
      },
      checkTimeString: () => {
        const originalTime = store.formattedTime.value;
        const cleanTime = cleanFormattedTime.value;
        console.log("🕐 时间字符串检查:", {
          original: originalTime,
          clean: cleanTime,
          originalCharCodes: Array.from(originalTime).map(char => ({
            char,
            code: char.charCodeAt(0)
          })),
          cleanCharCodes: Array.from(cleanTime).map(char => ({
            char,
            code: char.charCodeAt(0)
          })),
          hasQuotes: /['""`""'']/.test(originalTime),
          timestamp: new Date().toLocaleTimeString()
        });
      },
      testTimeout: (seconds = 10) => {
        console.log(`🧪 测试计时器超时功能，${seconds} 秒后触发`);
        // 调用 store 的测试方法
        const exerciseStore = (window as any).exerciseStore;
        if (exerciseStore?.testTimeout) {
          exerciseStore.testTimeout(seconds);
        } else {
          console.warn("store 测试方法不可用");
        }
      },
      testAiWaiting: () => {
        console.log("🧪 测试AI等待逻辑");
        const exerciseStore = (window as any).exerciseStore;
        if (exerciseStore?.testAiWaiting) {
          exerciseStore.testAiWaiting();
        } else {
          console.warn("store AI测试方法不可用");
        }
      },
      testTimeoutWithAi: (timeoutSeconds = 5, aiResponseSeconds = 3) => {
        console.log(
          `🧪 测试完整流程：${timeoutSeconds}秒后超时，${aiResponseSeconds}秒后AI回答完成`
        );
        const exerciseStore = (window as any).exerciseStore;
        if (exerciseStore?.testTimeoutWithAi) {
          exerciseStore.testTimeoutWithAi(timeoutSeconds, aiResponseSeconds);
        } else {
          console.warn("store 完整测试方法不可用");
        }
      },
      forceFixLoading: () => {
        console.log("🚨 强制修复loading状态");
        store.loading.value = false;
        // 设置一个持续监控，每100ms检查一次
        const fixInterval = setInterval(() => {
          if (
            store.loading.value &&
            store.isPracticing.value &&
            store.isTimerRunning.value
          ) {
            console.warn("🔧 检测到异常loading状态，自动修复");
            store.loading.value = false;
          }
        }, 100);

        // 10秒后停止监控
        setTimeout(() => {
          clearInterval(fixInterval);
          console.log("🔧 停止loading状态监控");
        }, 10000);
      }
    };
    console.log("🔧 Loading调试工具已添加到 window.exerciseDebug");
  }, 1000);
}

// 使用defineModel实现双向绑定
const drawerVisible = defineModel<boolean>("visible", { default: false });

// 定义事件
const emit = defineEmits(["success", "finish"]);

// 组件引用
const chatAreaRef = ref<InstanceType<typeof ChatArea> | null>(null);

// 使用统一状态管理
const store = useExerciseStore();

// 清理格式化时间中的引号
const cleanFormattedTime = computed(() => {
  const timeStr = store.formattedTime.value;

  // 调试：打印原始时间字符串
  if (import.meta.env.DEV && timeStr !== "00:00:00") {
    console.log("🕐 时间字符串调试:", {
      original: timeStr,
      charCodes: Array.from(timeStr).map(char => ({
        char,
        code: char.charCodeAt(0)
      })),
      hasQuotes: /['""]/.test(timeStr),
      length: timeStr.length
    });
  }

  // 移除所有可能的引号字符（包括中文引号）
  return timeStr.replace(/['""`""'']/g, "");
});

/**
 * 获取ChatArea组件需要的回调函数
 */
const getIsMessageStreamPlaying = (messageId: string) => {
  try {
    const chatState = store.chatState();
    return chatState?.isMessageStreamPlaying(messageId) || false;
  } catch (error) {
    console.warn("获取消息流播放状态失败:", error);
    return false;
  }
};

const getShouldDisablePlayButton = (
  messageId: string,
  isCurrentPlaying: boolean
) => {
  try {
    const chatState = store.chatState();
    if (chatState && chatState.shouldDisablePlayButton) {
      return chatState.shouldDisablePlayButton(messageId, isCurrentPlaying);
    }
    return false;
  } catch (error) {
    console.warn("获取播放按钮禁用状态失败:", error);
    return false;
  }
};

const getCompleteStreamAudioPlayback = (messageId: string) => {
  try {
    const chatState = store.chatState();
    return chatState?.completeStreamAudioPlayback(messageId);
  } catch (error) {
    console.warn("完成流式音频播放失败:", error);
    return Promise.resolve();
  }
};

// 初始化状态 - 现在完全依赖store
console.log("组件初始化，检查store状态:", {
  storeHasCourseInfo: !!store.courseInfo.value
});

// 如果store中有courseInfo，设置hooks回调
if (store.courseInfo.value) {
  console.log("🔄 Store中已有courseInfo，设置hooks回调");
  nextTick(() => {
    setupHooksCallbacks();
  });
}

// 监听 store 中的 courseInfo 变化
watch(
  () => store.courseInfo.value,
  (newCourseInfo, oldCourseInfo) => {
    console.log("Store courseInfo 发生变化:", {
      oldCourseId: oldCourseInfo?.trainingTaskCourseId,
      newCourseId: newCourseInfo?.trainingTaskCourseId,
      oldMaxDuration: oldCourseInfo?.course?.course?.maxDuration,
      newMaxDuration: newCourseInfo?.course?.course?.maxDuration,
      hasChange:
        oldCourseInfo?.trainingTaskCourseId !==
        newCourseInfo?.trainingTaskCourseId
    });

    // 如果是第一次设置courseInfo（从undefined到有值），或者课程ID发生变化
    const isFirstTime = !oldCourseInfo && newCourseInfo;
    const isCourseChanged =
      oldCourseInfo?.trainingTaskCourseId !==
      newCourseInfo?.trainingTaskCourseId;

    if (isFirstTime || isCourseChanged) {
      console.log(
        isFirstTime
          ? "首次设置store courseInfo"
          : "课程ID发生变化，清理状态并重新初始化"
      );

      // 清理音频播放资源
      if (chatAreaRef.value) {
        chatAreaRef.value.cleanupAudioPlayer();
      }

      // 设置hooks回调，确保hooks已经初始化
      if (newCourseInfo) {
        nextTick(() => {
          setupHooksCallbacks();
        });
      }

      console.log("Store courseInfo初始化/切换状态完成");
    }
  },
  { deep: true, immediate: true } // 添加 immediate: true 确保立即执行
);

// 监听抽屉可见性变化，确保在抽屉关闭时立即清理计时器
watch(drawerVisible, (newVisible, oldVisible) => {
  console.log("练习抽屉可见性变化:", {
    oldVisible,
    newVisible,
    hasStore: !!store,
    isTimerRunning: store.isTimerRunning.value
  });

  // 当抽屉从显示变为隐藏时，立即停止练习并清理状态
  if (oldVisible && !newVisible) {
    console.log("练习抽屉关闭，停止练习并清理超时状态");
    // 停止练习并清理所有状态，包括超时状态
    store.stopPractice();
    // 额外确保超时状态被清理
    store.stopSpeechCountdown();
  }
});

// 监听 loading 状态，防止在练习过程中意外被设置为 true
watch(
  () => store.loading.value,
  (newLoading, oldLoading) => {
    // 只在抽屉打开且不是在完成练习时监控
    if (drawerVisible.value && newLoading && !oldLoading) {
      console.warn("⚠️ 检测到loading状态被意外设置为true:", {
        drawerVisible: drawerVisible.value,
        isPracticing: store.isPracticing.value,
        isTimerRunning: store.isTimerRunning.value,
        timestamp: new Date().toLocaleTimeString()
      });

      // 如果是在练习过程中（不是完成练习），强制清除loading状态
      if (store.isPracticing.value && store.isTimerRunning.value) {
        console.log("🔧 强制清除意外的loading状态");
        nextTick(() => {
          store.loading.value = false;
        });
      }
    }
  }
);
/**
 * 处理放弃练习
 */
async function handleAbandonPractice() {
  const confirmed = await store.abandonPractice();
  if (confirmed) {
    // 清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 关闭抽屉
    drawerVisible.value = false;
  }
}

/**
 * 处理结束练习 - 从RecordingControl组件触发
 */
async function handleFinishPractice(data?: any) {
  console.log("练习完成，接收到的数据:", data);

  // 如果传入了数据，直接使用（来自RecordingControl组件）
  if (data) {
    // 清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 关闭抽屉并通知父组件
    drawerVisible.value = false;
    emit("finish", data);
    return;
  }

  // 否则使用store的完成方法
  const result = await store.finishPractice();
  if (result) {
    // 清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 关闭抽屉并通知父组件
    drawerVisible.value = false;
    emit("finish", result);
  }
}

/**
 * 抽屉打开事件处理 - 重置所有状态
 */
function handleDrawerOpen() {
  console.log("练习抽屉打开，重置所有状态", {
    storeHasCourseInfo: !!store.courseInfo.value,
    currentMode: store.exerciseMode.value,
    isSpeechCountdownActive: store.isSpeechCountdownActive.value
  });

  // 立即清除loading状态
  store.loading.value = false;

  // 确保倒计时状态被清理
  if (store.isSpeechCountdownActive.value) {
    console.log("🛑 抽屉打开时发现倒计时在运行，立即停止");
    store.stopSpeechCountdown();
  }

  // 检查课程信息，现在完全依赖store
  if (!store.courseInfo.value) {
    console.warn("Store中无课程信息，无法开始练习");
    // 即使没有课程信息，也要确保loading状态被清除
    store.loading.value = false;
    return;
  }

  // 使用 nextTick 确保组件完全渲染后再进行初始化
  nextTick(() => {
    // 清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 确保hooks回调已设置
    setupHooksCallbacks();

    // 检查hooks状态
    console.log("🔍 抽屉打开时hooks状态检查:", {
      hasAudioRecorderState: !!store.audioRecorderState(),
      hasChatState: !!store.chatState(),
      hasKeyboardRecordingState: !!store.keyboardRecordingState(),
      hasExerciseState: !!store.exerciseState(),
      hasTimerState: !!store.timerState()
    });

    // 立即启动计时器，不等待hooks初始化
    console.log("🚀 立即启动计时器");
    store.forceStartTimer();

    // 立即清除loading状态（在计时器启动后）
    store.loading.value = false;

    // 延迟启动完整练习流程，确保hooks完全初始化
    setTimeout(() => {
      console.log("延迟开始完整练习流程，检查hooks状态:", {
        hasTimerState: !!store.timerState(),
        hasChatState: !!store.chatState(),
        hasAudioRecorderState: !!store.audioRecorderState(),
        hasExerciseState: !!store.exerciseState(),
        storeHasCourseInfo: !!store.courseInfo.value
      });

      // 再次确保loading状态被清除
      store.loading.value = false;

      console.log("即将调用 store.startPractice()");
      try {
        // 由于计时器已经启动，这里主要是设置其他状态
        store.startPractice();
        console.log("store.startPractice() 调用完成");

        // 立即清除loading状态（在startPractice后）
        store.loading.value = false;
      } catch (error) {
        console.error("调用 store.startPractice() 时出错:", error);
        console.log("🚨 由于错误，确保计时器正在运行");
        // 即使出错，也要确保loading状态被清除
        store.loading.value = false;
        // 计时器应该已经在运行，这里只是确保状态正确
        if (!store.isTimerRunning.value) {
          store.forceStartTimer();
        }
      }

      // 最终状态检查和清理
      setTimeout(() => {
        console.log("🔍 最终状态检查:", {
          timer: store.timer.value,
          isTimerRunning: store.isTimerRunning.value,
          formattedTime: store.formattedTime,
          isPracticing: store.isPracticing.value,
          loading: store.loading.value
        });

        // 强制清除loading状态
        store.loading.value = false;

        // 双重保险：如果计时器仍未运行，再次强制启动
        if (!store.isTimerRunning.value) {
          console.warn("⚠️ 双重保险：计时器仍未运行，再次强制启动");
          store.forceStartTimer();
          // 启动后再次清除loading
          store.loading.value = false;
        } else {
          console.log("✅ 计时器运行正常，loading状态已清除");
        }
      }, 200);

      console.log("练习抽屉状态重置完成");
    }, 50); // 减少延迟时间，加快初始化速度

    // 额外的安全检查：确保loading状态最终被清除
    setTimeout(() => {
      console.log("🔒 最终安全检查:", {
        isTimerRunning: store.isTimerRunning.value,
        timer: store.timer.value,
        formattedTime: store.formattedTime,
        loading: store.loading.value
      });

      // 最终强制清除loading状态
      store.loading.value = false;
      console.log("🎯 最终强制清除loading状态完成");

      if (!store.isTimerRunning.value) {
        console.error("🚨 最终检查：计时器仍未启动，强制启动！");
        store.forceStartTimer();
        // 启动后再次清除loading
        store.loading.value = false;
      }
    }, 1000);
  });
}

/**
 * 关闭抽屉前的确认
 */
function handleClose() {
  if (store.isPracticing.value && store.hasUserMessages.value) {
    store.abandonPractice().then(confirmed => {
      if (confirmed) {
        // 清理音频播放资源
        if (chatAreaRef.value) {
          chatAreaRef.value.cleanupAudioPlayer();
        }
        drawerVisible.value = false;
      }
    });
  } else {
    // 即使没有练习内容，也要清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }
    drawerVisible.value = false;
  }
}

/**
 * 设置hooks回调函数
 */
function setupHooksCallbacks() {
  console.log("设置hooks回调函数");

  try {
    const audioRecorderState = store.audioRecorderState();
    const chatState = store.chatState();
    const keyboardRecordingState = store.keyboardRecordingState();

    console.log("🔍 Hooks状态检查:", {
      hasAudioRecorderState: !!audioRecorderState,
      hasChatState: !!chatState,
      hasKeyboardRecordingState: !!keyboardRecordingState
    });

    // 设置自动完成回调
    store.setAutoFinishCallback(result => {
      console.log("收到自动完成练习回调:", result);
      // 清理音频播放资源
      if (chatAreaRef.value) {
        chatAreaRef.value.cleanupAudioPlayer();
      }
      // 关闭抽屉
      drawerVisible.value = false;
      // 通知父组件练习完成
      emit("finish", result);
    });

    if (audioRecorderState) {
      console.log("✅ 设置音频录音回调");
      audioRecorderState.setCallbacks({
        onTextRecognized: (text: string, startTime: number, msgId: string) => {
          store.sendMessage(text, startTime, msgId);
        },
        onRecordingStateChange: () => {
          store.syncHooksStates();
        },
        onRecordingCancelled: () => {
          // 录音取消或识别失败时，恢复发言倒计时
          store.cancelRecording();
        }
      });
    } else {
      console.warn("⚠️ audioRecorderState 不存在，无法设置录音回调");
    }

    if (keyboardRecordingState) {
      console.log("✅ 设置键盘录音回调");
      keyboardRecordingState.setCallbacks({
        onStartRecording: () => {
          console.log("🎤 键盘录音开始回调触发");
          if (store.canStartRecording.value) {
            console.log("✅ 可以开始录音，调用 store.startRecording()");
            store.startRecording();
          } else {
            console.warn("⚠️ 当前状态不允许开始录音");
          }
        },
        onStopRecording: () => {
          console.log("🛑 键盘录音停止回调触发");
          store.stopRecording();
        },
        onCancelRecording: () => {
          console.log("❌ 键盘录音取消回调触发");
          store.cancelRecording();
        }
      });
    } else {
      console.warn("⚠️ keyboardRecordingState 不存在，无法设置键盘录音回调");
    }

    if (chatState) {
      chatState.setChatAreaCallbacks({
        onShowLoading: () => {
          if (chatAreaRef.value) {
            chatAreaRef.value.showLoading();
          }
        },
        onHideLoading: () => {
          if (chatAreaRef.value) {
            chatAreaRef.value.hideLoading();
          }
        },
        onScrollToBottom: () => {
          if (chatAreaRef.value) {
            chatAreaRef.value.scrollToBottom();
          }
        },
        onStartStreamAudio: (msgId: string) => {
          if (chatAreaRef.value) {
            chatAreaRef.value.startStreamAudio(msgId);
          }
        },
        onHandleStreamAudioChunk: (audioChunk: ArrayBuffer) => {
          if (chatAreaRef.value) {
            chatAreaRef.value.handleStreamAudioChunk(audioChunk);
          }
        },
        onCompleteStreamAudio: () => {
          if (chatAreaRef.value) {
            chatAreaRef.value.completeStreamAudio();
          }
        },
        onCompleteStreamAudioPlayback: (msgId: string) => {
          const currentChatState = store.chatState();
          if (currentChatState) {
            currentChatState.completeStreamAudioPlayback(msgId);
          }
        },
        onForceStopStreamAudio: () => {
          if (chatAreaRef.value) {
            chatAreaRef.value.forceStopStreamAudio();
          }
        },
        onPlayAudio: message => {
          if (chatAreaRef.value) {
            chatAreaRef.value.playAudio(message);
          }
        },
        onStopAudio: () => {
          if (chatAreaRef.value) {
            chatAreaRef.value.stopAudio();
          }
        }
      });
    }
  } catch (error) {
    console.error("设置hooks回调函数失败:", error);
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log("练习组件挂载");

  // 初始化聊天区域
  if (chatAreaRef.value) {
    chatAreaRef.value.initAudioPlayer();
  }

  // 确保hooks回调已设置（如果store中已有courseInfo）
  if (store.courseInfo.value) {
    console.log("🔄 组件挂载时设置hooks回调");
    nextTick(() => {
      setupHooksCallbacks();
    });
  }
});

// 组件卸载时清理资源
onBeforeUnmount(() => {
  // 停止练习
  store.stopPractice();

  // 清理聊天区域资源
  if (chatAreaRef.value) {
    chatAreaRef.value.cleanupAudioPlayer();
  }
});
</script>

<style scoped>
.exercise-drawer {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.timer-box {
  color: #1890ff;
}
</style>
