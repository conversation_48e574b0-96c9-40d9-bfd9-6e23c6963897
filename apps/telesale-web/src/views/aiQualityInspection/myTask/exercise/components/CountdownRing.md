# CountdownRing 倒计时圆环组件

## 组件概述

CountdownRing 是专门为对练机器人功能设计的倒计时圆环业务组件，用于在语音按钮外围显示发言倒计时进度。

## 功能特性

- 🎯 **专用业务组件**: 专门为对练机器人倒计时功能设计
- 🎨 **动态颜色**: 根据剩余时间占比自动变化颜色
- ⏰ **超时提示**: 倒计时结束时显示超时提示
- 🔄 **平滑动画**: 支持平滑的进度过渡动画
- 📦 **插槽支持**: 通过插槽包装任意内容

## 使用方式

### 基本用法

```vue
<template>
  <CountdownRing>
    <!-- 在这里放置需要包装的内容，如按钮 -->
    <el-button circle>
      <el-icon><Microphone /></el-icon>
    </el-button>
  </CountdownRing>
</template>

<script setup>
import CountdownRing from './CountdownRing.vue';
</script>
```

### 在RecordingControl中的使用

```vue
<CountdownRing>
  <el-button
    :type="getButtonType()"
    circle
    :icon="getButtonIcon()"
    @click="toggleRecording"
    :disabled="isButtonDisabled"
    size="large"
    :class="getButtonClass()"
    :title="getButtonTooltip()"
  />
</CountdownRing>
```

## 状态管理

组件直接使用对练机器人的统一状态管理 `useExerciseStore()`，自动获取以下状态：

- `store.isSpeechCountdownActive.value` - 是否正在倒计时
- `store.speechCountdown.value` - 倒计时剩余时间
- `store.speechInterval.value` - 倒计时总时长
- `store.speechTimeoutStatus.value` - 倒计时超时状态

组件内部计算属性：

- `speechCountdownProgress` - 倒计时进度百分比（基于store状态计算）
- `speechCountdownColor` - 倒计时圆环颜色（基于进度计算）

## 视觉效果

### 圆环颜色变化

| 剩余时间占比 | 颜色 | 色值 | 状态 |
|-------------|------|------|------|
| 100%-75% | 绿色 | #67c23a | 正常 |
| 75%-50% | 蓝色 | #409eff | 注意 |
| 50%-25% | 黄色 | #e6a23c | 警告 |
| 25%-0% | 红色 | #f56c6c | 紧急 |

### 超时提示

当倒计时结束时，会在组件下方显示超时提示：
- 提示文字：`当前发言已经超时，请尽快回复～`
- 样式：红色背景，白色文字，圆角边框
- 动画：淡入向上滑动效果

## 技术实现

### 圆环绘制

使用SVG绘制倒计时圆环：
- 背景圆环：灰色 (#e5e7eb)，宽度2px
- 进度圆环：动态颜色，宽度3px
- 视图框：60x60，圆心(30,30)，半径26

### 进度计算

```typescript
const circumference = computed(() => 2 * Math.PI * 26);
const strokeDashoffset = computed(() => {
  const progress = store.speechCountdownProgress.value;
  return circumference.value * (1 - progress / 100);
});
```

### 样式特点

- 容器尺寸：48px × 48px
- 圆环层级：z-index: 1，不响应鼠标事件
- 动画效果：300ms 线性过渡
- 超时提示：淡入向上滑动动画

## 组件结构

```
CountdownRing.vue
├── template
│   ├── countdown-ring-container (容器)
│   ├── svg (倒计时圆环)
│   │   ├── circle (背景圆环)
│   │   └── circle (进度圆环)
│   ├── slot (插槽内容)
│   └── timeout-toast (超时提示)
├── script
│   ├── useExerciseStore (状态管理)
│   ├── circumference (圆环周长)
│   └── strokeDashoffset (进度偏移)
└── style
    ├── .countdown-ring-container
    ├── .countdown-ring
    ├── .timeout-toast
    └── @keyframes fadeInUp
```

## 注意事项

1. **业务专用**: 该组件专门为对练机器人设计，不考虑通用性
2. **状态依赖**: 组件依赖 `useExerciseStore` 状态管理
3. **插槽使用**: 通过默认插槽包装需要添加倒计时的内容
4. **样式隔离**: 使用 scoped 样式，避免样式冲突
5. **性能优化**: 使用 computed 计算属性，避免不必要的重新计算

## 相关文件

- `store.ts` - 倒计时状态管理
- `RecordingControl.vue` - 使用该组件的录音控制组件
- `语音按钮倒计时圆环实现.md` - 整体功能实现文档
