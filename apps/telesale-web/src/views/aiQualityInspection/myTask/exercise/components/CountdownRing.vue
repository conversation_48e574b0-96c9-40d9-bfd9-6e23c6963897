<!--
 * @Date         : 2025-06-25
 * @Description  : 对练机器人倒计时圆环组件 - 语音按钮倒计时功能
 * <AUTHOR> AI Assistant
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="countdown-ring-container relative">
    <!-- 倒计时超时提示 - 放在圆环上方 -->
    <div
      v-if="
        store.speechTimeoutStatus.value === 'timeout' &&
        store.speechCountdown.value <= 0
      "
      class="timeout-toast absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-3 py-1 rounded text-sm whitespace-nowrap z-10"
    >
      当前发言已经超时，请尽快回复～
    </div>

    <!-- 倒计时圆环 -->
    <svg
      v-if="store.isSpeechCountdownActive.value"
      class="countdown-ring absolute inset-0 w-full h-full"
      :class="getRingAnimationClass()"
      viewBox="0 0 100 100"
    >
      <!-- 背景圆环 -->
      <circle
        cx="50"
        cy="50"
        r="41"
        fill="none"
        stroke="#e5e7eb"
        stroke-width="5.04"
        class="ring-background"
      />
      <!-- 进度圆环 -->
      <circle
        cx="50"
        cy="50"
        r="41"
        fill="none"
        :stroke="speechCountdownColor"
        stroke-width="6.3"
        stroke-linecap="round"
        :stroke-dasharray="circumference"
        :stroke-dashoffset="strokeDashoffset"
        transform="rotate(-90 50 50)"
        class="ring-progress"
        :class="getProgressAnimationClass()"
      />
      <!-- 外圈光晕效果 -->
      <circle
        v-if="speechCountdownProgress <= 25"
        cx="50"
        cy="50"
        r="41"
        fill="none"
        :stroke="speechCountdownColor"
        stroke-width="1.26"
        stroke-opacity="0.3"
        class="ring-glow"
      />
    </svg>

    <!-- 插槽内容 - 通常是录音按钮 -->
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useExerciseStore } from "../store";

// 使用对练机器人统一状态管理
const store = useExerciseStore();

/**
 * 倒计时圆环进度百分比（0-100）
 */
const speechCountdownProgress = computed(() => {
  if (!store.isSpeechCountdownActive.value || store.speechInterval.value === 0)
    return 0;
  return (store.speechCountdown.value / store.speechInterval.value) * 100;
});

/**
 * 倒计时圆环颜色
 */
const speechCountdownColor = computed(() => {
  const progress = speechCountdownProgress.value;
  if (progress >= 75) return "#67c23a"; // 绿色
  if (progress >= 50) return "#409eff"; // 蓝色
  if (progress >= 25) return "#e6a23c"; // 黄色
  return "#f56c6c"; // 红色
});

/**
 * 圆环周长
 */
const circumference = computed(() => {
  const radius = 41; // 与SVG中的r值保持一致，圆环在按钮外圈外，保持6px间距
  return 2 * Math.PI * radius;
});

/**
 * 圆环进度偏移量（逆时针减少，从满到空）
 */
const strokeDashoffset = computed(() => {
  const progress = speechCountdownProgress.value;
  // 逆时针减少：从0偏移开始（满圆），随着时间减少，偏移量增加（圆环减少）
  return circumference.value * (1 - progress / 100);
});

/**
 * 获取圆环动画类
 */
function getRingAnimationClass() {
  const classes = ["ring-enter"];

  // 低时间警告动画
  if (
    speechCountdownProgress.value <= 25 &&
    speechCountdownProgress.value > 0
  ) {
    classes.push("ring-warning");
  }

  return classes.join(" ");
}

/**
 * 获取进度条动画类
 */
function getProgressAnimationClass() {
  const classes = ["progress-smooth"];

  // 脉冲动画
  if (store.isSpeechCountdownActive.value) {
    classes.push("progress-pulse");
  }

  // 低时间闪烁
  if (
    speechCountdownProgress.value <= 10 &&
    speechCountdownProgress.value > 0
  ) {
    classes.push("progress-blink");
  }

  return classes.join(" ");
}
</script>

<style scoped>
/* 录音按钮容器样式 */
.countdown-ring-container {
  width: 92px;
  height: 92px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 倒计时圆环样式 */
.countdown-ring {
  pointer-events: none;
  z-index: 1;
}

/* 圆环入场动画 */
.ring-enter {
  animation: ringFadeInScale 0.5s ease-out;
}

/* 圆环警告动画 */
.ring-warning {
  animation: ringWarning 1s ease-in-out infinite;
}

/* 背景圆环样式 */
.ring-background {
  transition: stroke 0.3s ease;
}

/* 进度圆环基础样式 */
.ring-progress {
  transition: stroke 0.3s ease;
}

/* 进度条平滑过渡 */
.progress-smooth {
  transition: stroke-dashoffset 0.3s ease-linear;
}

/* 进度条脉冲动画 */
.progress-pulse {
  animation: progressPulse 2s ease-in-out infinite;
}

/* 进度条闪烁动画（低时间警告） */
.progress-blink {
  animation: progressBlink 0.5s ease-in-out infinite;
}

/* 光晕效果 */
.ring-glow {
  animation: glowPulse 1.5s ease-in-out infinite;
}

/* 超时提示样式 */
.timeout-toast {
  animation: fadeInDown 0.3s ease-out;
}

/* 动画定义 */
@keyframes ringFadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes ringWarning {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes progressPulse {
  0%,
  100% {
    stroke-width: 6.3;
    opacity: 1;
  }
  50% {
    stroke-width: 7.56;
    opacity: 0.8;
  }
}

@keyframes progressBlink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes glowPulse {
  0%,
  100% {
    stroke-width: 1.26;
    stroke-opacity: 0.3;
  }
  50% {
    stroke-width: 2.52;
    stroke-opacity: 0.6;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate(-50%, -10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}
</style>
