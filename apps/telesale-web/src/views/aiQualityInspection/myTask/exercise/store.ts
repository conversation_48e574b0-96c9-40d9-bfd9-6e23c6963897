/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 练习模块统一状态管理 - 使用VueUse
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, computed } from "vue";
import { createGlobalState } from "@vueuse/core";
import { ElMessage } from "element-plus";
import {
  WorkerTrainingTaskCourseInstance,
  WorkerTrainingTaskCourseUpdateRequest,
  finishWorkerTrainingTaskCourse,
  getWorkerTrainingTaskCourseInfo,
  getWorkerTrainingTaskCourseHistoricalData
} from "/@/api/AIQualityInspection/taskManagement";
import { useUserStoreHook } from "/@/store/modules/user";

// 导入hooks
import { useAudioRecorder } from "./hooks/useAudioRecorder";
import { useTimer } from "./hooks/useTimer";
import { useKeyboardRecording } from "./hooks/useKeyboardRecording";
import { useChat } from "./hooks/useChat";
import { useExercise } from "./hooks/useExercise";

/**
 * 消息接口
 */
interface Message {
  content: string;
  isBot: boolean;
  index: string;
  startTime?: number;
  /** 消息发送时是否处于超时状态 */
  wasTimeout?: boolean;
}

/**
 * 角色信息接口
 */
interface RoleInfo {
  role: string;
  roleName: string;
  roleIntroduction: string;
  personality: string;
}

/**
 * 练习模式枚举
 */
export enum ExerciseMode {
  VOICE_TEXT = "voice_text", // 语音+文本对练
  TEXT_ONLY = "text_only" // 纯文本对练
}

/**
 * 创建全局练习状态管理
 */
export const useExerciseStore = createGlobalState(() => {
  // ===== 基础状态 =====
  const loading = ref(false);
  const isPracticing = ref(false);
  const isAudioPlaying = ref(false);

  // ===== 练习模式 =====
  const exerciseMode = ref<ExerciseMode>(ExerciseMode.VOICE_TEXT); // 默认为语音+文本模式

  // ===== 课程信息 =====
  const courseInfo = ref<WorkerTrainingTaskCourseInstance | null>(null);
  const taskMode = ref<string>("exam"); // 任务模式：practice 或 exam

  // ===== 练习时间相关 =====
  const practiceStartTime = ref<number>(0);
  const isWaitingForCompletion = ref<boolean>(false);

  // ===== 发言倒计时相关 =====
  const speechCountdown = ref<number>(0); // 剩余发言时间（秒）
  const isSpeechCountdownActive = ref<boolean>(false); // 是否正在倒计时
  const speechTimeoutStatus = ref<"normal" | "warning" | "timeout">("normal"); // 发言超时状态
  const speechCountdownTimer = ref<NodeJS.Timeout | null>(null);

  // ===== 自动完成相关 =====
  const isWaitingForTtsToFinish = ref<boolean>(false); // 是否正在等待TTS完成后自动完成练习
  const isWaitingForAiToFinish = ref<boolean>(false); // 是否正在等待AI回答完成后自动完成练习
  const ttsFinishTimeout = ref<number | null>(null); // TTS完成超时定时器
  const aiFinishTimeout = ref<number | null>(null); // AI回答完成超时定时器

  // ===== 消息和聊天 =====
  const messages = ref<Message[]>([]);
  const isAiResponding = ref(false);
  const isTtsWaiting = ref(false);
  const audioData = ref<Map<string, ArrayBuffer>>(new Map());

  // ===== 录音相关 =====
  const isRecording = ref(false);
  const recordingTime = ref(0);
  const recordingText = ref("请开始发言");
  const recordingStartTime = ref<number>(0);
  const isProcessingAudio = ref(false); // 是否正在处理语音（3000毫秒延迟期间）

  // ===== 计时器相关 =====
  const timer = ref(0);
  const isTimerRunning = ref(false);
  const maxDurationSeconds = ref(3600); // 默认60分钟

  // ===== 角色信息 =====
  const roleInfo = computed<RoleInfo | null>(() => {
    if (!courseInfo.value) return null;

    const course = courseInfo.value.course?.course;
    if (!course) return null;

    return {
      role: course.botRole || "customer",
      roleName: course.botName || "客户",
      roleIntroduction:
        course.target || course.backgroundDesc || "练习对话角色",
      personality: course.conversationReq || "按照课程要求进行对话"
    };
  });

  // ===== Hooks实例 =====
  let audioRecorderState: ReturnType<typeof useAudioRecorder> | null = null;
  let timerState: ReturnType<typeof useTimer> | null = null;
  let keyboardRecordingState: ReturnType<typeof useKeyboardRecording> | null =
    null;
  let chatState: ReturnType<typeof useChat> | null = null;
  let exerciseState: ReturnType<typeof useExercise> | null = null;

  // ===== 状态同步定时器 =====
  const syncIntervals: {
    timer: NodeJS.Timeout | null;
    chat: NodeJS.Timeout | null;
    recording: NodeJS.Timeout | null;
    exercise: NodeJS.Timeout | null;
  } = {
    timer: null,
    chat: null,
    recording: null,
    exercise: null
  };

  // 注意：移除了store自己的计时器，统一使用useTimer hook

  // ===== 事件回调 =====
  const onAutoFinishCallback = ref<((result: any) => void) | null>(null);

  // ===== 计算属性 =====

  /**
   * 格式化的计时器时间
   */
  const formattedTime = computed(() => {
    const hours = Math.floor(timer.value / 3600);
    const minutes = Math.floor((timer.value % 3600) / 60);
    const seconds = timer.value % 60;

    const timeString = [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      seconds.toString().padStart(2, "0")
    ].join(":");

    // 确保返回的字符串不包含引号
    return timeString.replace(/"/g, "");
  });

  /**
   * 角色名称
   */
  const roleName = computed(() => {
    return (
      roleInfo.value?.roleName ||
      courseInfo.value?.course?.course?.botName ||
      "客户"
    );
  });

  /**
   * 最大持续时间（分钟）
   */
  const maxDuration = computed(() => {
    return courseInfo.value?.course?.course?.maxDuration || 60;
  });

  /**
   * 对话间隔时长（秒）
   */
  const speechInterval = computed(() => {
    const interval = courseInfo.value?.course?.course?.duration;
    // 如果后端没有设置或返回null，则使用30秒作为前端默认值
    return interval != null ? interval : 30;
  });

  /**
   * 是否有用户消息
   */
  const hasUserMessages = computed(() => {
    return messages.value.filter(msg => !msg.isBot).length > 0;
  });

  /**
   * 是否可以开始录音
   */
  const canStartRecording = computed(() => {
    return (
      !isRecording.value &&
      !isProcessingAudio.value &&
      !isAiResponding.value &&
      !isTtsWaiting.value &&
      !isAudioPlaying.value
    );
  });

  /**
   * 是否可以完成练习
   */
  const canFinishPractice = computed(() => {
    return (
      hasUserMessages.value &&
      !isWaitingForCompletion.value &&
      !isAiResponding.value &&
      !isTtsWaiting.value &&
      !isAudioPlaying.value
    );
  });

  /**
   * 是否可以放弃练习
   */
  const canAbandonPractice = computed(() => {
    return !isWaitingForCompletion.value;
  });

  // ===== 方法 =====

  /**
   * 清理状态同步定时器
   */
  const clearSyncIntervals = () => {
    if (syncIntervals.timer) {
      clearInterval(syncIntervals.timer);
      syncIntervals.timer = null;
    }
    if (syncIntervals.chat) {
      clearInterval(syncIntervals.chat);
      syncIntervals.chat = null;
    }
    if (syncIntervals.recording) {
      clearInterval(syncIntervals.recording);
      syncIntervals.recording = null;
    }
    if (syncIntervals.exercise) {
      clearInterval(syncIntervals.exercise);
      syncIntervals.exercise = null;
    }
  };

  /**
   * 设置状态监听器，实时同步hooks状态到store
   */
  const setupStateWatchers = () => {
    // 清理之前的定时器
    clearSyncIntervals();

    // 同步计时器状态 - 实时同步确保界面更新
    if (timerState) {
      syncIntervals.timer = setInterval(() => {
        if (timerState) {
          timer.value = timerState.timer.value;
          isTimerRunning.value = timerState.isRunning.value;
        } else {
          if (syncIntervals.timer) {
            clearInterval(syncIntervals.timer);
            syncIntervals.timer = null;
          }
        }
      }, 100); // 每100ms同步一次，确保计时器实时更新
    }

    // 监听聊天状态
    if (chatState) {
      // 使用更频繁的检查来确保不错过状态变化
      syncIntervals.chat = setInterval(() => {
        if (chatState) {
          const oldIsTtsWaiting = isTtsWaiting.value;
          const oldIsAiResponding = isAiResponding.value;

          // 获取新的状态值
          const newIsAiResponding = chatState.isAiResponding.value;
          const newIsTtsWaiting = chatState.isTtsWaiting.value;

          // 更新状态
          messages.value = chatState.messages.value;
          isAiResponding.value = newIsAiResponding;
          isTtsWaiting.value = newIsTtsWaiting;
          audioData.value = chatState.audioData.value;

          // 检查AI状态变化
          if (oldIsAiResponding !== newIsAiResponding) {
            // 检查是否从AI回答中变为完成状态
            const conditionResult = oldIsAiResponding && !newIsAiResponding;

            if (conditionResult) {
              // 检查是否需要触发自动完成
              if (isWaitingForAiToFinish.value) {
                // 语音模式：需要等待TTS完成
                if (isTextOnlyMode.value || !newIsTtsWaiting) {
                  console.log(
                    "AI回答完成且TTS也完成（或纯文本模式），触发自动完成"
                  );
                  handleAiFinishedAutoComplete();
                } else {
                  console.log("AI回答完成但TTS仍在等待，暂不触发自动完成");
                }
              } else if (
                isTextOnlyMode.value &&
                hasUserMessages.value &&
                !isTimerRunning.value
              ) {
                // 在纯文本模式下，如果计时器已停止（说明已超时），且有用户消息，也应该自动完成
                // 纯文本模式不需要等待TTS，可以直接完成
                console.log(
                  "纯文本模式AI回答完成（计时器已停止），触发自动完成"
                );
                handleAiFinishedAutoComplete();
              }
            }
          }

          // 检查TTS状态变化：从等待变为完成
          if (
            oldIsTtsWaiting &&
            !newIsTtsWaiting &&
            isWaitingForTtsToFinish.value
          ) {
            handleTtsFinishedAutoComplete();
          }
        } else {
          if (syncIntervals.chat) {
            clearInterval(syncIntervals.chat);
            syncIntervals.chat = null;
          }
        }
      }, 50); // 改为每50ms同步一次，提高检测频率
    }

    // 监听录音状态
    if (audioRecorderState) {
      // 使用定时器定期同步录音状态
      syncIntervals.recording = setInterval(() => {
        if (audioRecorderState) {
          isRecording.value = audioRecorderState.isRecording.value;
          recordingTime.value = audioRecorderState.recordingTime.value;
          recordingText.value = audioRecorderState.recordingText.value;
          recordingStartTime.value =
            audioRecorderState.recordingStartTime.value;
        } else {
          if (syncIntervals.recording) {
            clearInterval(syncIntervals.recording);
            syncIntervals.recording = null;
          }
        }
      }, 100); // 每100ms同步一次
    }

    // 注意：移除了练习状态同步，现在 store 直接管理这些状态
  };

  /**
   * 初始化练习状态
   */
  const initializeExercise = (
    newCourseInfo: WorkerTrainingTaskCourseInstance
  ) => {
    console.log("初始化练习状态", {
      hasCourseInfo: !!newCourseInfo,
      courseId: newCourseInfo?.trainingTaskCourseId,
      maxDuration: newCourseInfo?.course?.course?.maxDuration,
      currentMessagesLength: messages.value.length
    });

    // 先彻底清理旧的hooks实例
    if (chatState) {
      console.log("🧹 清理旧的chatState实例");
      chatState.clearChat();
    }
    if (audioRecorderState) {
      audioRecorderState.cleanup();
    }
    if (keyboardRecordingState) {
      keyboardRecordingState.removeEventListeners();
    }
    if (timerState) {
      timerState.stopTimer(true);
    }

    // 重置所有状态（但不清理courseInfo）
    resetAllStatesExceptCourseInfo();

    // 设置课程信息
    courseInfo.value = newCourseInfo;

    // 初始化hooks
    if (newCourseInfo) {
      try {
        // 创建新的hooks实例
        exerciseState = useExercise(courseInfo);
        const maxDurationMinutes =
          newCourseInfo.course?.course?.maxDuration || 60;
        timerState = useTimer(maxDurationMinutes);
        audioRecorderState = useAudioRecorder();
        keyboardRecordingState = useKeyboardRecording();
        chatState = useChat();

        // 设置AI状态变化回调
        if (chatState) {
          chatState.setChatAreaCallbacks({
            onAiStateChange: (isResponding: boolean) => {
              // 检测从true到false的变化（AI回答完成）
              if (isAiResponding.value && !isResponding) {
                // 更新状态
                isAiResponding.value = isResponding;

                // 文本模式：当机器人文本生成完毕后，启动发言倒计时
                if (
                  isTextOnlyMode.value &&
                  hasUserMessages.value &&
                  isPracticing.value
                ) {
                  console.log("AI回复完成（纯文本模式），启动发言倒计时", {
                    hasUserMessages: hasUserMessages.value,
                    isPracticing: isPracticing.value,
                    isTextOnlyMode: isTextOnlyMode.value
                  });
                  startSpeechCountdown();
                }

                // 检查是否需要触发自动完成
                if (isWaitingForAiToFinish.value) {
                  // 纯文本模式可以直接完成，语音模式需要等待TTS
                  if (isTextOnlyMode.value) {
                    console.log("纯文本模式AI回答完成，直接触发自动完成");
                    handleAiFinishedAutoComplete();
                  } else {
                    console.log(
                      "语音模式AI回答完成，等待TTS完成后再触发自动完成"
                    );
                    // 语音模式下，等待TTS状态变化时再触发
                  }
                } else if (
                  isTextOnlyMode.value &&
                  hasUserMessages.value &&
                  !isTimerRunning.value
                ) {
                  // 在纯文本模式下，如果计时器已停止（说明已超时），且有用户消息，也应该自动完成
                  console.log(
                    "纯文本模式AI回答完成（计时器已停止），直接触发自动完成"
                  );
                  handleAiFinishedAutoComplete();
                }
              } else {
                // 正常更新状态
                isAiResponding.value = isResponding;
              }
            }
          });
        }

        // 手动初始化键盘事件监听器（因为不在组件中，onMounted不会触发）
        if (keyboardRecordingState) {
          keyboardRecordingState.initEventListeners();
        }

        // 更新最大时长
        maxDurationSeconds.value = maxDurationMinutes * 60;

        // 设置状态同步监听器
        setupStateWatchers();

        // 确保loading状态被清除
        loading.value = false;

        console.log("练习状态初始化完成");
      } catch (error) {
        console.error("初始化练习状态失败:", error);
        // 即使初始化失败，也要清除loading状态
        loading.value = false;
        resetAllStates();
      }
    } else {
      // 如果没有课程信息，确保loading状态被清除
      loading.value = false;
    }
  };

  /**
   * 重置所有状态（除了courseInfo）
   */
  const resetAllStatesExceptCourseInfo = () => {
    console.log("🔄 开始重置所有状态（除courseInfo外）", {
      currentMode: exerciseMode.value,
      isSpeechCountdownActive: isSpeechCountdownActive.value,
      hasUserMessages: hasUserMessages.value
    });

    // 基础状态
    loading.value = false;
    isPracticing.value = false;
    isAudioPlaying.value = false;

    // 练习时间相关
    practiceStartTime.value = 0;
    isWaitingForCompletion.value = false;

    // 发言倒计时相关 - 强制停止并重置
    console.log("🛑 强制停止发言倒计时");
    stopSpeechCountdown();

    // 额外确保倒计时状态完全清理
    speechCountdown.value = 0;
    isSpeechCountdownActive.value = false;
    speechTimeoutStatus.value = "normal";
    if (speechCountdownTimer.value) {
      clearInterval(speechCountdownTimer.value);
      speechCountdownTimer.value = null;
    }

    // 自动完成相关
    isWaitingForTtsToFinish.value = false;
    isWaitingForAiToFinish.value = false;
    if (ttsFinishTimeout.value) {
      clearTimeout(ttsFinishTimeout.value);
      ttsFinishTimeout.value = null;
    }
    if (aiFinishTimeout.value) {
      clearTimeout(aiFinishTimeout.value);
      aiFinishTimeout.value = null;
    }

    // 消息和聊天
    console.log("🧹 清理消息列表，当前消息数量:", messages.value.length);
    messages.value = [];
    isAiResponding.value = false;
    isTtsWaiting.value = false;
    audioData.value.clear();
    console.log("✅ 消息列表已清理，新的消息数量:", messages.value.length);

    // 录音相关
    isRecording.value = false;
    recordingTime.value = 0;
    recordingText.value = "请开始发言";
    recordingStartTime.value = 0;

    // 计时器相关
    timer.value = 0;
    isTimerRunning.value = false;

    // 注意：已移除store自己的计时器，现在统一使用useTimer hook

    // 清理状态同步定时器
    clearSyncIntervals();

    // 清理hooks实例
    if (timerState) {
      timerState.stopTimer();
    }
    if (audioRecorderState) {
      audioRecorderState.cleanup();
    }
    if (keyboardRecordingState) {
      keyboardRecordingState.removeEventListeners();
    }
    if (chatState) {
      chatState.clearChat();
    }

    audioRecorderState = null;
    timerState = null;
    keyboardRecordingState = null;
    chatState = null;
    exerciseState = null;

    console.log("✅ 所有状态已重置（除courseInfo外）", {
      isSpeechCountdownActive: isSpeechCountdownActive.value,
      speechCountdown: speechCountdown.value,
      messagesLength: messages.value.length,
      currentMode: exerciseMode.value
    });
  };

  /**
   * 重置所有状态
   */
  const resetAllStates = () => {
    // 重置除courseInfo外的所有状态
    resetAllStatesExceptCourseInfo();

    // 清理课程信息
    courseInfo.value = null;
  };

  /**
   * 强制清除loading状态（调试用）
   */
  const forceClearLoading = () => {
    loading.value = false;
  };

  /**
   * 开始发言倒计时
   */
  const startSpeechCountdown = () => {
    // 安全检查：只有在练习进行中且有用户消息时才启动倒计时
    if (!isPracticing.value) {
      console.warn("⚠️ 练习未开始，跳过倒计时启动");
      return;
    }

    if (!hasUserMessages.value) {
      console.warn("⚠️ 无用户消息，跳过倒计时启动");
      return;
    }

    // 如果倒计时已经在运行，先停止
    if (isSpeechCountdownActive.value) {
      console.log("🔄 倒计时已在运行，先停止再重新启动");
      stopSpeechCountdown();
    }

    const interval = speechInterval.value;
    console.log("🚀 启动发言倒计时", {
      interval,
      mode: exerciseMode.value,
      hasUserMessages: hasUserMessages.value,
      isPracticing: isPracticing.value
    });

    speechCountdown.value = interval;
    isSpeechCountdownActive.value = true;
    speechTimeoutStatus.value = "normal";

    // 清除之前的计时器
    if (speechCountdownTimer.value) {
      clearInterval(speechCountdownTimer.value);
    }

    speechCountdownTimer.value = setInterval(() => {
      speechCountdown.value--;

      // 更新超时状态
      const remaining = speechCountdown.value;
      const total = interval;
      const percentage = (remaining / total) * 100;

      if (percentage <= 0) {
        speechTimeoutStatus.value = "timeout";
        // 只在第一次到达0时显示提示，避免重复提示
        if (speechCountdown.value === 1) {
          ElMessage.warning("当前发言已经超时，请尽快回复～");
        }
      } else if (percentage <= 25) {
        speechTimeoutStatus.value = "timeout";
      } else if (percentage <= 50) {
        speechTimeoutStatus.value = "warning";
      } else if (percentage <= 75) {
        speechTimeoutStatus.value = "warning";
      } else {
        speechTimeoutStatus.value = "normal";
      }

      // 倒计时结束但不停止计时器，继续显示超时状态
      if (speechCountdown.value <= 0) {
        speechCountdown.value = 0;
      }
    }, 1000);
  };

  /**
   * 暂停发言倒计时
   */
  const pauseSpeechCountdown = () => {
    if (speechCountdownTimer.value) {
      clearInterval(speechCountdownTimer.value);
      speechCountdownTimer.value = null;
    }
  };

  /**
   * 停止发言倒计时
   */
  const stopSpeechCountdown = () => {
    if (speechCountdownTimer.value) {
      clearInterval(speechCountdownTimer.value);
      speechCountdownTimer.value = null;
    }
    speechCountdown.value = 0;
    isSpeechCountdownActive.value = false;
    speechTimeoutStatus.value = "normal";
  };

  /**
   * 重置发言倒计时
   */
  const resetSpeechCountdown = () => {
    stopSpeechCountdown();
    // 重置为当前配置的间隔时间
    speechCountdown.value = speechInterval.value;
  };

  /**
   * 设置计时器超时回调（内部方法）
   */
  const setupTimerCallback = () => {
    if (!timerState) return;

    // 设置时间到达回调
    timerState.setTimeUpCallback(async () => {
      console.log(`达到最大时间(${maxDuration.value}分钟)，自动完成练习`);

      // 停止计时器
      isTimerRunning.value = false;

      // 自动完成练习
      try {
        // 如果有用户消息，自动完成练习
        if (hasUserMessages.value) {
          console.log("时间到达，检查是否可以立即完成练习");

          // 检查TTS状态（语音模式）
          if (isTtsWaiting.value) {
            console.log("TTS正在生成中，等待TTS完成后自动完成练习");
            ElMessage.info("练习时间已达上限，等待语音生成完成后自动完成练习");

            // 设置等待TTS完成的标志
            isWaitingForTtsToFinish.value = true;

            // 设置超时保护机制（30秒后强制完成）
            ttsFinishTimeout.value = window.setTimeout(async () => {
              console.log("TTS完成超时，强制完成练习");
              isWaitingForTtsToFinish.value = false;
              ElMessage.warning("语音生成超时，强制完成练习");

              // 强制完成练习（跳过TTS状态检查）
              const result = await forceFinishPractice();
              if (result && onAutoFinishCallback.value) {
                onAutoFinishCallback.value(result);
              }
            }, 30000); // 30秒超时
          }
          // 检查AI回答状态（纯文本模式或语音模式下AI正在回答）
          else if (isAiResponding.value) {
            ElMessage.info("练习时间已达上限，等待AI回答完成后自动完成练习");

            // 设置等待AI回答完成的标志
            isWaitingForAiToFinish.value = true;
          } else {
            // TTS和AI都未在处理中，可以立即完成
            console.log("TTS和AI都未在处理中，立即自动完成练习");
            ElMessage.info("练习时间已达上限，系统自动完成练习");

            // 调用完成练习方法
            const result = await finishPractice();
            if (result) {
              // 通知父组件练习完成
              console.log("自动完成练习成功，准备关闭抽屉");
              if (onAutoFinishCallback.value) {
                onAutoFinishCallback.value(result);
              }
            }
          }
        } else {
          // 如果没有用户消息，只是停止练习
          console.log("时间到达，但没有用户消息，只停止练习");
          ElMessage.warning("练习时间已达上限，但您还没有开始对话");
          stopPractice();
        }
      } catch (error) {
        console.error("自动完成练习失败:", error);
        ElMessage.error("自动完成练习失败，请手动完成");
        stopPractice();
      }
    });
  };

  /**
   * 强制启动计时器（测试用）
   */
  const forceStartTimer = () => {
    // 设置基本状态
    isPracticing.value = true;
    isTimerRunning.value = true;
    practiceStartTime.value = Math.floor(Date.now() / 1000);
    timer.value = 0;

    // 使用统一的计时器Hook
    if (timerState) {
      timerState.resetTimer();
      timerState.startTimer();

      // 设置超时回调
      setupTimerCallback();
    }

    // 确保loading状态被清除
    loading.value = false;
  };

  /**
   * 开始练习
   */
  const startPractice = () => {
    console.log("开始练习");

    if (!courseInfo.value) {
      console.error("无法开始练习：courseInfo为空");
      forceStartTimer();
      return;
    }

    // 设置练习状态
    isPracticing.value = true;

    // 如果练习开始时间未设置，设置为当前时间
    if (!practiceStartTime.value) {
      practiceStartTime.value = Math.floor(Date.now() / 1000);
    }

    // 启动统一的计时器Hook
    if (timerState && !isTimerRunning.value) {
      isTimerRunning.value = true;

      // 设置超时回调
      setupTimerCallback();

      // 启动计时器
      timerState.startTimer();
    } else if (timerState && isTimerRunning.value) {
      // 如果计时器已经在运行，确保回调已设置
      setupTimerCallback();
    }

    // 注意：移除了重复的备用机制，现在统一使用useTimer hook

    console.log("练习已开始");
  };

  /**
   * 暂停练习（不清理状态，只停止计时器）
   */
  const pausePractice = () => {
    console.log("暂停练习");

    // 使用统一的计时器Hook
    if (timerState) {
      timerState.pauseTimer(); // 使用pauseTimer而不是stopTimer
    }
    isTimerRunning.value = false;

    console.log("练习已暂停");
  };

  /**
   * 停止练习
   */
  const stopPractice = () => {
    console.log("停止练习");

    isPracticing.value = false;

    // 使用统一的计时器Hook
    if (timerState) {
      timerState.stopTimer(true); // 完全清理包括回调函数
    }
    isTimerRunning.value = false;

    if (audioRecorderState) {
      audioRecorderState.cleanup();
    }

    // 清理发言倒计时和超时状态
    stopSpeechCountdown();

    console.log("练习已停止，超时状态已清理");
  };

  /**
   * 同步hooks状态到store
   */
  const syncHooksStates = () => {
    if (audioRecorderState) {
      isRecording.value = audioRecorderState.isRecording.value;
      recordingTime.value = audioRecorderState.recordingTime.value;
      recordingText.value = audioRecorderState.recordingText.value;
      recordingStartTime.value = audioRecorderState.recordingStartTime.value;
      isProcessingAudio.value = audioRecorderState.isProcessingAudio.value;
    }

    // 同步计时器状态（现在统一使用useTimer hook）
    if (timerState) {
      timer.value = timerState.timer.value;
      isTimerRunning.value = timerState.isRunning.value;
    }

    if (chatState) {
      messages.value = chatState.messages.value;
      isAiResponding.value = chatState.isAiResponding.value;
      isTtsWaiting.value = chatState.isTtsWaiting.value;
      audioData.value = chatState.audioData.value;
    }

    // 注意：移除了练习状态同步，现在 store 直接管理这些状态
  };

  /**
   * 开始录音
   */
  const startRecording = () => {
    if (!audioRecorderState) {
      console.error("audioRecorderState 不存在，无法开始录音");
      return;
    }

    if (!canStartRecording.value) {
      console.warn("当前状态不允许开始录音");
      return;
    }

    // 开始录音时暂停发言倒计时
    if (isSpeechCountdownActive.value) {
      console.log("开始录音，暂停发言倒计时");
      pauseSpeechCountdown();
    }

    audioRecorderState.startRecording();
    syncHooksStates();
  };

  /**
   * 停止录音
   */
  const stopRecording = () => {
    if (!audioRecorderState) return;

    audioRecorderState.stopRecording();
    syncHooksStates();
  };

  /**
   * 取消录音
   */
  const cancelRecording = () => {
    if (!audioRecorderState) return;

    audioRecorderState.cancelRecording();
    syncHooksStates();

    // 取消录音后，如果之前有倒计时且还有剩余时间，则恢复倒计时
    if (speechCountdown.value > 0 && hasUserMessages.value) {
      console.log("取消录音，恢复发言倒计时");
      // 重新启动倒计时，使用剩余时间
      isSpeechCountdownActive.value = true;
      speechTimeoutStatus.value = "normal";

      // 清除之前的计时器
      if (speechCountdownTimer.value) {
        clearInterval(speechCountdownTimer.value);
      }

      speechCountdownTimer.value = setInterval(() => {
        speechCountdown.value--;

        // 更新超时状态
        const remaining = speechCountdown.value;
        const total = speechInterval.value;
        const percentage = (remaining / total) * 100;

        if (percentage <= 0) {
          speechTimeoutStatus.value = "timeout";
          // 只在第一次到达0时显示提示，避免重复提示
          if (speechCountdown.value === 1) {
            ElMessage.warning("当前发言已经超时，请尽快回复～");
          }
        } else if (percentage <= 25) {
          speechTimeoutStatus.value = "timeout";
        } else if (percentage <= 50) {
          speechTimeoutStatus.value = "warning";
        } else if (percentage <= 75) {
          speechTimeoutStatus.value = "warning";
        } else {
          speechTimeoutStatus.value = "normal";
        }

        // 倒计时结束但不停止计时器，继续显示超时状态
        if (speechCountdown.value <= 0) {
          speechCountdown.value = 0;
        }
      }, 1000);
    }
  };

  /**
   * 发送消息
   */
  const sendMessage = async (
    content: string,
    startTime?: number,
    msgId?: string
  ) => {
    if (!chatState || !exerciseState) {
      console.error("无法发送消息：缺少必要状态");
      return;
    }

    // 在 store 层面进行状态检查
    if (!content.trim()) {
      console.warn("消息内容为空，无法发送");
      return;
    }

    if (!isPracticing.value) {
      console.warn("练习未开始，无法发送消息");
      return;
    }

    if (isAiResponding.value) {
      console.warn("AI正在响应中，无法发送消息");
      return;
    }

    // 调用 exerciseState 的简化版本检查（不包含 isPracticing 检查）
    if (!exerciseState.canSendMessage(content, isAiResponding.value)) {
      return;
    }

    // 记录发送消息时的超时状态
    const wasTimeoutWhenSending = speechTimeoutStatus.value === "timeout";

    // 发送消息时停止发言倒计时
    if (isSpeechCountdownActive.value) {
      console.log("发送消息，停止发言倒计时");
      stopSpeechCountdown();
    }

    await chatState.sendMessage(
      content,
      exerciseState.getWorkerTrainingTaskId(),
      exerciseState.getTrainingTaskCourseId(),
      roleInfo.value,
      startTime,
      msgId,
      isTextOnlyMode.value, // 传入练习模式
      wasTimeoutWhenSending // 传入超时状态
    );

    syncHooksStates();
  };

  /**
   * 处理音频播放状态变化
   * @param isPlaying 是否正在播放
   * @param isAiReply 是否为AI实时回复的音频（默认true，用于区分用户手动播放的历史音频）
   */
  const handleAudioPlayStateChange = (isPlaying: boolean, isAiReply: boolean = true) => {
    isAudioPlaying.value = isPlaying;

    if (exerciseState) {
      exerciseState.handleAudioPlayStateChange(isPlaying);
    }

    // 语音模式：只有当AI实时回复的语音播放完毕后，才启动发言倒计时
    // 用户手动播放的历史音频播放结束不应该重置倒计时
    if (
      !isPlaying &&
      isAiReply &&
      !isTextOnlyMode.value &&
      hasUserMessages.value &&
      isPracticing.value
    ) {
      console.log("AI实时回复语音播放完毕（语音+文本模式），启动发言倒计时", {
        hasUserMessages: hasUserMessages.value,
        isPracticing: isPracticing.value,
        isTextOnlyMode: isTextOnlyMode.value,
        isVoiceTextMode: isVoiceTextMode.value,
        isAiReply: isAiReply
      });
      startSpeechCountdown();
    } else if (!isPlaying && !isAiReply) {
      console.log("用户手动播放的历史音频播放结束，不重置倒计时", {
        isAiReply: isAiReply,
        isPracticing: isPracticing.value
      });
    }
  };

  /**
   * 放弃练习
   */
  const abandonPractice = async () => {
    if (!exerciseState || !canAbandonPractice.value) {
      console.warn("无法放弃练习：状态不允许");
      return false;
    }

    const confirmed = await exerciseState.handleAbandonPractice();
    if (confirmed) {
      stopPractice();
      resetAllStates();
    }

    return confirmed;
  };

  /**
   * 处理TTS完成后的自动完成练习
   */
  const handleTtsFinishedAutoComplete = async () => {
    if (!isWaitingForTtsToFinish.value) {
      return; // 不在等待状态，直接返回
    }

    console.log("TTS完成，执行自动完成练习");

    // 清理超时定时器
    if (ttsFinishTimeout.value) {
      clearTimeout(ttsFinishTimeout.value);
      ttsFinishTimeout.value = null;
    }

    // 重置等待标志
    isWaitingForTtsToFinish.value = false;

    // 确保违禁词提示被清理
    if (chatState) {
      try {
        // 调用chatState的clearChat方法，它会清理违禁词提示
        // 但我们只需要清理违禁词提示，不需要清理整个聊天
        // 所以直接访问forbiddenWordCheck实例
        const chatStateInstance = chatState as any;
        if (chatStateInstance.forbiddenWordCheck?.clearForbiddenWordMessage) {
          chatStateInstance.forbiddenWordCheck.clearForbiddenWordMessage();
        }
      } catch (error) {
        console.warn("清理违禁词提示失败:", error);
      }
    }

    try {
      // 执行完成练习
      const result = await executeFinishPractice();
      if (result && onAutoFinishCallback.value) {
        console.log("TTS完成后自动完成练习成功");
        ElMessage.success("语音生成完成，练习已自动完成");
        onAutoFinishCallback.value(result);
      }
    } catch (error) {
      console.error("TTS完成后自动完成练习失败:", error);
      ElMessage.error("自动完成练习失败，请手动完成");
    }
  };

  /**
   * 处理AI回答完成后的自动完成练习
   */
  const handleAiFinishedAutoComplete = async () => {
    // 在纯文本模式下，即使 isWaitingForAiToFinish 为 false，
    // 如果有用户消息且AI不在回答中，也应该尝试自动完成
    if (!isWaitingForAiToFinish.value) {
      if (
        isTextOnlyMode.value &&
        hasUserMessages.value &&
        !isAiResponding.value
      ) {
        // 纯文本模式：虽然不在等待AI完成状态，但条件满足，继续执行自动完成
      } else {
        return;
      }
    }

    // 清理超时定时器
    if (aiFinishTimeout.value) {
      clearTimeout(aiFinishTimeout.value);
      aiFinishTimeout.value = null;
    }

    // 重置等待标志
    isWaitingForAiToFinish.value = false;

    // 确保违禁词提示被清理
    if (chatState) {
      try {
        const chatStateInstance = chatState as any;
        if (chatStateInstance.forbiddenWordCheck?.clearForbiddenWordMessage) {
          chatStateInstance.forbiddenWordCheck.clearForbiddenWordMessage();
        }
      } catch (error) {
        console.warn("清理违禁词提示失败:", error);
      }
    }

    try {
      // 执行完成练习
      const result = await executeFinishPractice();

      if (result && onAutoFinishCallback.value) {
        ElMessage.success("AI回答完成，练习已自动完成");
        onAutoFinishCallback.value(result);
      }
    } catch (error) {
      console.error("AI回答完成后自动完成练习失败:", error);
      ElMessage.error("自动完成练习失败，请手动完成");
    }
  };

  /**
   * 强制完成练习（跳过状态检查，用于超时等特殊情况）
   */
  const forceFinishPractice = async () => {
    console.log("强制完成练习，跳过状态检查");
    return await executeFinishPractice();
  };

  /**
   * 完成练习
   */
  const finishPractice = async () => {
    if (!canFinishPractice.value) {
      console.warn("无法完成练习：状态不允许");

      // 根据具体状态给出更详细的提示
      if (isTtsWaiting.value) {
        ElMessage.warning("TTS正在生成中，请等待完成后再完成练习");
      } else if (isAiResponding.value) {
        ElMessage.warning("AI正在思考中，请等待回复完成后再完成练习");
      } else if (isAudioPlaying.value) {
        ElMessage.warning("语音正在播放中，请等待播放完成后再完成练习");
      } else if (!hasUserMessages.value) {
        ElMessage.warning("请先进行对话后再完成练习");
      } else {
        ElMessage.warning("当前状态不允许完成练习，请稍后再试");
      }

      return null;
    }

    return await executeFinishPractice();
  };

  /**
   * 执行完成练习的核心逻辑
   */
  const executeFinishPractice = async () => {
    try {
      loading.value = true;
      // 设置等待完成状态，防止重复调用
      isWaitingForCompletion.value = true;

      // 清理TTS等待相关状态
      if (ttsFinishTimeout.value) {
        clearTimeout(ttsFinishTimeout.value);
        ttsFinishTimeout.value = null;
      }
      isWaitingForTtsToFinish.value = false;

      // 清理AI等待相关状态
      if (aiFinishTimeout.value) {
        clearTimeout(aiFinishTimeout.value);
        aiFinishTimeout.value = null;
      }
      isWaitingForAiToFinish.value = false;

      // 清理违禁词提示
      if (chatState) {
        try {
          const chatStateInstance = chatState as any;
          if (chatStateInstance.forbiddenWordCheck?.clearForbiddenWordMessage) {
            chatStateInstance.forbiddenWordCheck.clearForbiddenWordMessage();
          }
        } catch (error) {
          console.warn("清理违禁词提示失败:", error);
        }
      }

      // 获取用户信息
      const userStore = useUserStoreHook();
      const userMsg = userStore.userMsg;

      // 获取当前时间戳（秒级）
      const currentTimestamp = Math.floor(Date.now() / 1000);

      // 计算练习开始和结束时间
      const practiceStartTimeValue =
        practiceStartTime.value || currentTimestamp - timer.value;
      const practiceEndTime = currentTimestamp;
      const practiceDuration = timer.value;

      // 转换消息格式
      const conversationMessages = messages.value.map(msg => {
        const baseMessage = {
          role: msg.isBot ? "bot" : "user",
          content: msg.content,
          start: (msg.startTime || practiceStartTimeValue).toString(),
          key: msg.index
        };

        // 为用户消息添加 extra 字段（包含超时状态，无论是否超时都传递）
        if (!msg.isBot) {
          return {
            ...baseMessage,
            extra: {
              timeout: msg.wasTimeout || false
            }
          };
        }

        return baseMessage;
      });

      // 构建请求参数
      const requestData: WorkerTrainingTaskCourseUpdateRequest = {
        workerTrainingTaskId: courseInfo.value?.workerTrainingTaskId || "0",
        trainingTaskCourseId: courseInfo.value?.trainingTaskCourseId || "0",
        conversation: {
          workerId: userMsg.id || 0,
          trainingTaskCourseId: courseInfo.value?.trainingTaskCourseId || "0",
          duration: practiceDuration,
          messages: conversationMessages,
          begin: practiceStartTimeValue.toString(),
          end: practiceEndTime.toString()
        },
        // 根据练习模式设置 trainingMode 参数
        trainingMode: isTextOnlyMode.value ? "text" : "audio",
        // 传递任务模式参数
        taskMode: taskMode.value
      };

      // 调用完成练习接口
      await finishWorkerTrainingTaskCourse(requestData);

      ElMessage.success("练习完成成功！");

      // 停止练习
      stopPractice();

      // 通过历史数据接口获取最新的对话ID
      let updatedCourseInfo = courseInfo.value;
      try {
        console.log("🔄 练习完成后获取历史数据以获取最新对话ID");

        // 先调用历史数据接口获取最新的练习记录
        const historicalResponse = await getWorkerTrainingTaskCourseHistoricalData({
          workerTrainingTaskId: courseInfo.value?.workerTrainingTaskId,
          trainingTaskCourseId: courseInfo.value?.trainingTaskCourseId
        });

        if (historicalResponse?.data?.courses && historicalResponse.data.courses.length > 0) {
          // 按时间排序，获取最新的记录
          const sortedCourses = historicalResponse.data.courses
            .filter(course => course.finished && course.conversation?.id)
            .sort((a, b) =>
              new Date(b.finishedAt || 0).getTime() - new Date(a.finishedAt || 0).getTime()
            );

          if (sortedCourses.length > 0) {
            const latestCourse = sortedCourses[0];
            const latestConversationId = latestCourse.conversation?.id;

            if (latestConversationId) {
              console.log("✅ 从历史数据获取到最新对话ID:", latestConversationId);

              // 使用最新的对话ID调用课程详情接口
              const courseInfoResponse = await getWorkerTrainingTaskCourseInfo({
                workerTrainingTaskId: courseInfo.value?.workerTrainingTaskId,
                trainingTaskCourseId: courseInfo.value?.trainingTaskCourseId,
                taskMode: taskMode.value,
                workerTrainingConversationId: latestConversationId
              });

              if (courseInfoResponse?.data?.conversation?.id) {
                // 更新courseInfo，添加最新的对话ID
                updatedCourseInfo = {
                  ...courseInfo.value,
                  workerTrainingConversationId: courseInfoResponse.data.conversation.id
                };
                console.log("✅ 成功更新课程信息，对话ID:", courseInfoResponse.data.conversation.id);
              }
            }
          }
        }
      } catch (error) {
        console.warn("⚠️ 获取最新课程信息失败，使用原始课程信息:", error);
      }

      // 返回完成数据
      return {
        requestData,
        courseInfo: updatedCourseInfo,
        shouldOpenEvaluation: true,
        messages: messages.value,
        duration: timer.value,
        practiceStartTime: practiceStartTime.value,
        roleInfo: roleInfo.value
      };
    } catch (error) {
      console.error("完成练习失败:", error);
      ElMessage.error("完成练习失败，请重试");
      return null;
    } finally {
      loading.value = false;
      // 重置等待完成状态
      isWaitingForCompletion.value = false;
    }
  };

  /**
   * 设置课程信息（供外部调用）
   */
  const setCourseInfo = (newCourseInfo: WorkerTrainingTaskCourseInstance) => {
    console.log("🔄 设置课程信息", {
      newCourseId: newCourseInfo?.trainingTaskCourseId,
      currentCourseId: courseInfo.value?.trainingTaskCourseId,
      currentMessagesLength: messages.value.length
    });

    // 确保loading状态被清除
    loading.value = false;

    // 无论是否为同一个课程，都重新初始化以确保状态清理
    // 这样可以确保每次开始新练习时都有一个干净的状态
    initializeExercise(newCourseInfo);

    // 再次确保loading状态被清除（防止初始化过程中被设置）
    loading.value = false;
  };

  /**
   * 设置任务模式（供外部调用）
   */
  const setTaskMode = (mode: string) => {
    taskMode.value = mode;
    console.log("🎯 任务模式已设置:", mode);
  };

  /**
   * 设置自动完成回调
   */
  const setAutoFinishCallback = (callback: (result: any) => void) => {
    onAutoFinishCallback.value = callback;
  };

  /**
   * 设置练习模式
   */
  const setExerciseMode = (mode: ExerciseMode) => {
    console.log("🔄 设置练习模式", {
      oldMode: exerciseMode.value,
      newMode: mode,
      isSpeechCountdownActive: isSpeechCountdownActive.value
    });

    exerciseMode.value = mode;

    // 切换模式时，强制停止倒计时，避免状态残留
    if (isSpeechCountdownActive.value) {
      console.log("🛑 模式切换时停止倒计时");
      stopSpeechCountdown();
    }

    console.log("✅ 练习模式已设置", {
      currentMode: exerciseMode.value,
      isSpeechCountdownActive: isSpeechCountdownActive.value
    });
  };

  /**
   * 是否为纯文本模式
   */
  const isTextOnlyMode = computed(
    () => exerciseMode.value === ExerciseMode.TEXT_ONLY
  );

  /**
   * 是否为语音+文本模式
   */
  const isVoiceTextMode = computed(
    () => exerciseMode.value === ExerciseMode.VOICE_TEXT
  );

  return {
    // 状态
    loading,
    isPracticing,
    isAudioPlaying,
    exerciseMode,
    courseInfo,
    taskMode,
    practiceStartTime,
    isWaitingForCompletion,
    isWaitingForTtsToFinish,
    isWaitingForAiToFinish,
    messages,
    isAiResponding,
    isTtsWaiting,
    audioData,
    isRecording,
    recordingTime,
    recordingText,
    recordingStartTime,
    isProcessingAudio,
    timer,
    isTimerRunning,
    maxDurationSeconds,
    roleInfo,

    // 计算属性
    formattedTime,
    roleName,
    maxDuration,
    speechInterval,
    hasUserMessages,
    canStartRecording,
    canFinishPractice,
    canAbandonPractice,
    isTextOnlyMode,
    isVoiceTextMode,

    // 发言倒计时相关
    speechCountdown,
    isSpeechCountdownActive,
    speechTimeoutStatus,

    // 方法
    setCourseInfo,
    setTaskMode,
    setExerciseMode,
    initializeExercise,
    resetAllStates,
    startPractice,
    pausePractice,
    stopPractice,
    forceClearLoading,
    forceStartTimer,
    syncHooksStates,
    startRecording,
    stopRecording,
    cancelRecording,
    sendMessage,
    handleAudioPlayStateChange,
    abandonPractice,
    finishPractice,
    forceFinishPractice,
    setAutoFinishCallback,
    handleAiFinishedAutoComplete,
    executeFinishPractice,

    // 发言倒计时方法
    startSpeechCountdown,
    pauseSpeechCountdown,
    stopSpeechCountdown,
    resetSpeechCountdown,

    // 调试用的内部状态访问
    getAutoFinishCallback: () => onAutoFinishCallback.value,

    // Hooks实例引用
    audioRecorderState: () => audioRecorderState,
    timerState: () => timerState,
    keyboardRecordingState: () => keyboardRecordingState,
    chatState: () => chatState,
    exerciseState: () => exerciseState
  };
});
