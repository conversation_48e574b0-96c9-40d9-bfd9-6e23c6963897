<!--
 * @Date         : 2025-05-15 14:30:00
 * @Description  : 任务管理 - 新建/编辑任务弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑任务' : '新建任务'"
    width="600px"
    destroy-on-close
    @open="openDialog"
  >
    <nexus-form
      ref="formRef"
      v-model="form"
      label-width="100px"
      class="task-form"
    >
      <!-- 任务名称 -->
      <el-form-item
        label="任务名称"
        prop="name"
        :rules="[
          { required: true, message: '请输入任务名称', trigger: 'blur' },
          { max: 20, message: '任务名称不能超过20个字符', trigger: 'blur' }
        ]"
      >
        <el-input
          v-model="form.name"
          placeholder="请输入任务名称"
          maxlength="20"
          show-word-limit
        />
      </el-form-item>

      <!-- 任务模式 -->
      <el-form-item
        label="任务模式"
        prop="mode"
        :rules="[
          { required: true, message: '请选择任务模式', trigger: 'change' }
        ]"
      >
        <el-select
          v-model="form.mode"
          placeholder="请选择任务模式"
          style="width: 100%"
          :disabled="isEdit"
        >
          <el-option
            v-for="option in TASK_MODE_OPTIONS"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          >
            <div>
              <div>{{ option.label }}</div>
              <div style="font-size: 12px; color: #999; margin-top: 2px">
                {{ option.description }}
              </div>
            </div>
          </el-option>
        </el-select>
        <el-text v-if="isEdit" type="info" size="small" class="mt-5px">
          编辑任务时不允许修改任务模式
        </el-text>
      </el-form-item>

      <!-- 学习课程 -->
      <el-form-item
        label="学习课程"
        prop="selectedCourseIds"
        :rules="[
          { required: true, message: '请至少选择一个课程', trigger: 'change' },
          { validator: validateCourses, trigger: 'change' }
        ]"
      >
        <CourseSelector
          v-model:selectedCourseIds="form.selectedCourseIds"
          v-model:courses="form.courses"
          :is-edit="isEdit"
          :original-course-ids="originalData.courses.map(c => c.id)"
        />
      </el-form-item>

      <!-- 学员 -->
      <el-form-item
        label="学员"
        prop="acceptor"
        :rules="[{ required: true, message: '请选择学员', trigger: 'change' }]"
      >
        <div>
          <div class="flex items-center">
            <el-button
              type="primary"
              size="small"
              :icon="Plus"
              @click="organizationUserSelectVisible = true"
            >
              添加学员
            </el-button>
          </div>

          <div v-if="selectedStudents.length > 0" class="mt-10px">
            <el-tag
              v-for="(student, index) in selectedStudents"
              :key="index"
              class="mr-10px mb-10px"
              :closable="
                !(isEdit && originalData.acceptor.includes(student.id))
              "
              @close="removeStudent(index)"
            >
              {{ student.name }}
            </el-tag>
          </div>
        </div>
      </el-form-item>

      <!-- 起止时间 -->
      <el-form-item
        label="起止时间"
        prop="timeRange"
        :rules="[
          { required: true, message: '请选择起止时间', trigger: 'change' }
        ]"
      >
        <div class="flex items-center">
          <nexus-date-picker
            limitType="future"
            v-model="form.timeRange"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="w-full"
            :disabled="isEdit"
          />
        </div>
        <div class="text-gray-400 mt-5px text-12px">
          到达截止时间后，学员无法再练习课程
          <template v-if="isEdit">
            <br />
            <span class="text-red-500">编辑任务时不允许修改时间范围</span>
          </template>
        </div>
      </el-form-item>
    </nexus-form>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? "更新" : "创建" }}
      </el-button>
    </template>

    <!-- 学员选择弹窗 -->
    <OrganizationUserSelect
      v-model:visible="organizationUserSelectVisible"
      v-model:userList="selectedStudents"
      :format-target="formatStudents"
      :is-edit="isEdit"
      :original-user-ids="originalData.acceptor"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, defineModel, watch } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import NexusDatePicker from "/@/components/Nexus/NexusDatePicker/index.vue";
import dayjs from "dayjs";
import {
  createTrainingTask,
  updateTrainingTask,
  getTrainingTaskInfo,
  TrainingTaskCreateUpdateRequest,
  TASK_MODE,
  TASK_MODE_OPTIONS
} from "/@/api/AIQualityInspection/taskManagement";
import CourseSelector from "./CourseSelector.vue";
import OrganizationUserSelect from "../organizationSelect/index.vue";
import { useUserStoreHook } from "/@/store/modules/user";

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");

// 定义props
const props = defineProps({
  taskId: {
    type: Number,
    default: undefined
  }
});

// 定义emit
const emit = defineEmits(["success"]);

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 是否为编辑模式
const isEdit = computed(() => !!props.taskId);

// 表单数据
interface TaskForm {
  name: string;
  mode: string;
  courses: any[];
  acceptor: number[];
  timeRange: [string, string] | [];
  beginAt?: string;
  endAt?: string;
  selectedCourseIds: number[]; // 用于存储选中的课程ID
}

const form = reactive<TaskForm>({
  name: "",
  mode: TASK_MODE.EXAM, // 默认为考试模式
  courses: [],
  acceptor: [],
  timeRange: [],
  selectedCourseIds: []
});

// 已选学员
const selectedStudents = ref<any[]>([]);

// 学员选择弹窗可见性
const organizationUserSelectVisible = ref(false);

// 存储原始数据，用于编辑时的比较
const originalData = ref({
  courses: [] as any[],
  acceptor: [] as number[],
  timeRange: [] as string[]
});

// 格式化学员数据
function formatStudents(students: any[]) {
  return students.map(student => ({
    ...student,
    id: student.id,
    name: student.name
  }));
}

// 监听学员变化，更新表单数据
watch(
  selectedStudents,
  newVal => {
    form.acceptor = newVal.map(item => item.id);
  },
  { deep: true }
);

// 移除学员
function removeStudent(index: number) {
  const studentToRemove = selectedStudents.value[index];

  // 在编辑模式下，检查是否为原有学员
  if (
    isEdit.value &&
    originalData.value.acceptor.includes(studentToRemove.id)
  ) {
    ElMessage.warning("编辑任务时不允许删除已下发的学员");
    return;
  }

  selectedStudents.value.splice(index, 1);
  form.acceptor = selectedStudents.value.map(item => item.id);
}

// 验证课程
function validateCourses(_: any, value: any[], callback: any) {
  if (value.length === 0) {
    callback(new Error("请至少选择一个课程"));
  } else if (value.length > 20) {
    callback(new Error("最多只能选择20个课程"));
  } else {
    callback();
  }
}

// 打开弹窗时初始化表单
async function openDialog() {
  resetForm();

  if (isEdit.value && props.taskId) {
    try {
      loading.value = true;
      const res = await getTrainingTaskInfo(props.taskId);
      const taskData = res.data.task;

      // 填充表单数据
      form.name = taskData.name || "";
      form.mode = taskData.taskMode || TASK_MODE.EXAM; // 历史数据默认为考试模式

      // 处理课程数据
      const coursesData =
        taskData.courses?.map(course => ({
          id: course.trainingCourseId,
          name: course.course?.name || `课程${course.trainingCourseId}`,
          score: course.score
        })) || [];

      form.courses = coursesData;

      // 同步更新selectedCourseIds
      form.selectedCourseIds = coursesData.map(course => course.id);

      // 设置时间范围
      if (taskData.beginAt && taskData.endAt) {
        // 将时间戳转换为日期时间字符串用于显示
        form.timeRange = [
          dayjs.unix(Number(taskData.beginAt)).format("YYYY-MM-DD HH:mm:ss"),
          dayjs.unix(Number(taskData.endAt)).format("YYYY-MM-DD HH:mm:ss")
        ];
      }

      // 获取学员数据
      if (taskData.acceptor && taskData.acceptor.length > 0) {
        // 从接口获取学员数据
        const userStore = useUserStoreHook();
        const allAgentList = userStore.allAgentList || [];

        // 根据acceptor中的ID查找对应的学员信息
        selectedStudents.value = taskData.acceptor
          .map(id => {
            const agent = allAgentList.find(agent => agent.id === id);
            return agent ? { id: agent.id, name: agent.name } : null;
          })
          .filter(Boolean); // 过滤掉null值
      }

      form.acceptor = selectedStudents.value.map(item => item.id);

      // 保存原始数据，用于编辑时的比较
      originalData.value = {
        courses: JSON.parse(JSON.stringify(coursesData)),
        acceptor: [...(taskData.acceptor || [])],
        timeRange: [...form.timeRange]
      };
    } catch (error) {
      console.error("获取任务详情失败:", error);
      ElMessage.error("获取任务详情失败");
    } finally {
      loading.value = false;
    }
  }
}

// 重置表单
function resetForm() {
  form.name = "";
  form.mode = TASK_MODE.EXAM; // 重置为默认考试模式
  form.courses = [];
  form.acceptor = [];
  form.timeRange = [];
  form.selectedCourseIds = [];
  selectedStudents.value = [];
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    if (!form.timeRange || form.timeRange.length !== 2) {
      ElMessage.warning("请选择起止时间");
      return;
    }

    loading.value = true;

    const userStore = useUserStoreHook();

    // 构建请求数据
    const requestData: TrainingTaskCreateUpdateRequest = {
      name: form.name,
      taskMode: form.mode, // 添加任务模式
      workerId: userStore.userMsg.id, // 当前登录用户ID
      operator: userStore.userMsg.name, // 当前操作人
      courses: form.courses.map(course => ({
        trainingCourseId: course.id,
        score: course.score || 1
      })) as any, // 使用类型断言解决类型错误
      acceptor: form.acceptor, // 添加学员ID列表
      beginAt: "", // 临时值，将在下面设置
      endAt: "" // 临时值，将在下面设置
    };

    // 设置时间范围
    if (isEdit.value) {
      // 编辑模式下使用原始时间范围
      const beginTime = dayjs(originalData.value.timeRange[0]);
      const endTime = dayjs(originalData.value.timeRange[1]);

      if (beginTime.isValid() && endTime.isValid()) {
        requestData.beginAt = String(beginTime.unix());
        requestData.endAt = String(endTime.unix());
      }
    } else {
      // 新增模式下使用表单时间范围
      requestData.beginAt = String(dayjs(form.timeRange[0]).unix());
      requestData.endAt = String(dayjs(form.timeRange[1]).unix());
    }

    if (isEdit.value && props.taskId) {
      // 编辑模式
      await updateTrainingTask(props.taskId, requestData);
      ElMessage.success("任务更新成功");
    } else {
      // 新增模式
      await createTrainingTask(requestData);
      ElMessage.success("任务创建成功");
    }

    // 关闭弹窗并通知父组件刷新列表
    visible.value = false;
    emit("success");
  } catch (error) {
    console.error("保存任务失败:", error);
    ElMessage.error("保存任务失败");
  } finally {
    loading.value = false;
  }
}

// 监听弹窗关闭事件
watch(visible, newVal => {
  if (!newVal) {
    // 弹窗关闭时，重置表单状态
    resetForm();
  }
});

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<style scoped>
.task-form {
  padding-left: 20px;
  padding-right: 20px;
}
</style>
