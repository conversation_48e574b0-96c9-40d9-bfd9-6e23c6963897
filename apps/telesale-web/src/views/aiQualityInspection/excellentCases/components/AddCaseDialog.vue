<!--
 * @Date         : 2025-01-09
 * @Description  : 添加案例弹窗
 * @Autor        : AI Assistant
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="添加案例"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <nexus-form
      ref="formRef"
      v-model="formData"
      :rules="rules"
      label-width="140px"
    >
      <el-form-item label="添加案例类型：" prop="caseType">
        <el-select v-model="formData.caseType" placeholder="请选择添加案例类型">
          <el-option
            label="首通优秀录音"
            value="首通优秀录音"
            :disabled="props.rowData?.showPage === 1"
          />
          <el-option
            label="后续跟进录音"
            value="后续跟进录音"
            :disabled="props.rowData?.showPage === 2"
          />
        </el-select>
      </el-form-item>

      <!-- 显示当前案例状态提示 -->
      <el-form-item v-if="currentCaseStatus > 0">
        <el-text type="info" size="small">
          {{ currentCaseStatusText }}
        </el-text>
      </el-form-item>

      <el-form-item
        label="选择录音标签："
        prop="recordingTag"
        v-if="formData.caseType === '后续跟进录音'"
      >
        <el-select
          v-model="formData.recordingTag"
          placeholder="请选择后续跟进录音标签类型"
        >
          <el-option label="问题处理" value="问题处理" />
          <el-option label="优惠包装" value="优惠包装" />
          <el-option label="截杀关单" value="截杀关单" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>
    </nexus-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="AddCaseDialog">
import { reactive, watch, ref, computed } from "vue";
import { ElMessage } from "element-plus";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import { setExcellentCaseShowPage } from "/@/api/AIQualityInspection/excellentCases";

// 定义props
interface Props {
  visible: boolean;
  rowData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  rowData: undefined
});

// 定义事件
const emit = defineEmits<{
  "update:visible": [value: boolean];
  confirm: [data: any];
}>();

// 定义表单数据接口
interface FormData {
  caseType: string;
  recordingTag: string;
}

// 表单引用
const formRef = ref();

// 表单数据
const formData = reactive<FormData>({
  caseType: "",
  recordingTag: ""
});

// 表单校验规则
const rules = computed(() => {
  const baseRules: any = {
    caseType: [
      { required: true, message: "请选择添加案例类型", trigger: "change" }
    ]
  };

  // 只有当选择"后续跟进录音"时，录音标签才是必填的
  if (formData.caseType === "后续跟进录音") {
    baseRules.recordingTag = [
      { required: true, message: "请选择录音标签", trigger: "change" }
    ];
  }

  return baseRules;
});

// 提交状态
const submitting = ref(false);

// 计算属性：控制弹窗显示
const visible = computed({
  get: () => props.visible,
  set: (value: boolean) => {
    emit("update:visible", value);
  }
});

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  // 重置表单
  formData.caseType = "";
  formData.recordingTag = "";
};

// 确认提交
const handleConfirm = async () => {
  if (!props.rowData || !props.rowData.id) {
    ElMessage.error("案例数据无效");
    return;
  }

  // 表单验证
  try {
    await formRef.value?.validate();
  } catch (error) {
    return;
  }

  // 检查重复添加 - 更精确的检查
  const currentShowPage = props.rowData?.showPage || 0;
  const targetShowPage = formData.caseType === "后续跟进录音" ? 2 : 1;

  if (currentShowPage === targetShowPage) {
    const libraryName =
      formData.caseType === "后续跟进录音"
        ? "后续跟进录音库"
        : "首通优秀录音库";
    ElMessage.warning(`当前案例已在${libraryName}中，请勿重复添加~`);
    return;
  }

  submitting.value = true;
  try {
    // 构建添加参数
    const params: any = {
      id: props.rowData.id,
      actionId: props.rowData.actionId,
      caseType: formData.caseType
    };

    // 如果是后续跟进录音，添加录音标签
    if (formData.caseType === "后续跟进录音") {
      params.audioTags = formData.recordingTag ? [formData.recordingTag] : [];
      params.followUpRecording = true;
    } else {
      params.followUpRecording = false;
    }

    // 调用设置案例展示页面的API
    const showPageParams = {
      actionId: props.rowData.actionId,
      showPage: formData.caseType === "后续跟进录音" ? 2 : 1, // 1:首通优秀录音库，2:后续跟进录音库
      audioTags:
        formData.caseType === "后续跟进录音" && formData.recordingTag
          ? [formData.recordingTag]
          : undefined
    };

    await setExcellentCaseShowPage(showPageParams);

    // TODO: 如果需要其他添加案例的API调用，可以在这里添加
    // await addExcellentCase(params);

    // 回调父组件
    emit("confirm", {
      ...props.rowData,
      ...formData
    });

    ElMessage.success("添加成功");
    handleClose();
  } catch (error) {
    console.error("添加失败", error);
  } finally {
    submitting.value = false;
  }
};

// 计算属性：当前案例状态
const currentCaseStatus = computed(() => {
  return props.rowData?.showPage || 0;
});

// 计算属性：当前案例状态文本
const currentCaseStatusText = computed(() => {
  const showPage = props.rowData?.showPage || 0;
  switch (showPage) {
    case 1:
      return "当前案例已在首通优秀录音库中";
    case 2:
      return "当前案例已在后续跟进录音库中";
    default:
      return "当前案例尚未添加到任何录音库";
  }
});

// 监听案例类型变化，重置录音标签
watch(
  () => formData.caseType,
  newVal => {
    if (newVal !== "后续跟进录音") {
      formData.recordingTag = "";
    }
  }
);

// 监听props.rowData变化，重置表单数据
watch(
  () => props.rowData,
  newVal => {
    if (newVal) {
      // 重置表单数据
      formData.caseType = "";
      formData.recordingTag = "";
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
