<!--
 * @Date         : 2024-06-24
 * @Description  : AI人工复核对话框
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="人工复检"
    width="600px"
    :before-close="handleClose"
    destroy-on-close
  >
    <nexus-form
      ref="formRef"
      v-model="formData"
      :rules="rules"
      label-width="180px"
    >
      <el-form-item label="复检结果：" prop="reviewResult">
        <el-select v-model="formData.reviewResult" placeholder="请选择复检结果">
          <el-option label="通过" value="通过" />
          <el-option label="不通过" value="不通过" />
        </el-select>
      </el-form-item>

      <el-form-item
        label="复检评级："
        prop="reviewGrade"
        v-if="formData.reviewResult === '不通过'"
      >
        <el-select v-model="formData.reviewGrade" placeholder="请选择复检评级">
          <el-option label="A级" value="A" />
          <el-option label="B级" value="B" />
          <el-option label="C级" value="C" />
          <el-option label="D级" value="D" />
        </el-select>
      </el-form-item>

      <el-form-item
        label="未通过原因："
        prop="failReason"
        v-if="formData.reviewResult === '不通过'"
      >
        <el-select v-model="formData.failReason" placeholder="请选择未通过原因">
          <el-option label="信息缺失" value="信息缺失" />
          <el-option label="录音整体评级过高" value="录音整体评级过高" />
          <el-option label="录音整体评级过低" value="录音整体评级过低" />
          <el-option label="SOP评分不准" value="SOP评分不准" />
        </el-select>
      </el-form-item>

      <!-- 添加至后续跟进录音库选项 -->
      <el-form-item
        label="添加至后续跟进录音库："
        prop="addToFollowUp"
        v-if="showFollowUpOption"
      >
        <el-select v-model="formData.addToFollowUp" placeholder="请选择">
          <el-option label="否" value="否" />
          <el-option label="是" value="是" />
        </el-select>
      </el-form-item>

      <!-- 录音标签选择 -->
      <el-form-item
        label="选择录音标签："
        prop="recordingTag"
        v-if="formData.addToFollowUp === '是'"
      >
        <el-select
          v-model="formData.recordingTag"
          placeholder="请选择后续跟进录音标签类型"
        >
          <el-option label="问题处理" value="问题处理" />
          <el-option label="优惠包装" value="优惠包装" />
          <el-option label="截杀关单" value="截杀关单" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>
    </nexus-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, watch, ref, computed } from "vue";
import { ElMessage } from "element-plus";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import {
  updateExcellentCase,
  setExcellentCaseShowPage,
  type ExcellentCaseUpdateParams
} from "/@/api/AIQualityInspection/excellentCases";

// 定义表单数据接口
interface FormData {
  reviewResult: string;
  reviewGrade: string;
  failReason: string;
  addToFollowUp: string;
  recordingTag: string;
}

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");

// 表单数据
const formData = reactive<FormData>({
  reviewResult: "",
  reviewGrade: "",
  failReason: "",
  addToFollowUp: "否",
  recordingTag: ""
});

// 表单校验规则
const rules = computed(() => {
  const baseRules: any = {
    reviewResult: [
      { required: true, message: "请选择复检结果", trigger: "change" }
    ]
  };

  // 当复检结果为"不通过"时，需要校验评级和原因
  if (formData.reviewResult === "不通过") {
    baseRules.reviewGrade = [
      { required: true, message: "请选择复检评级", trigger: "change" }
    ];
    // baseRules.failReason = [
    //   { required: true, message: "请选择未通过原因", trigger: "change" }
    // ];

    // 当显示后续跟进选项时，需要校验该字段
    if (showFollowUpOption.value) {
      baseRules.addToFollowUp = [
        {
          required: true,
          message: "请选择是否添加至后续跟进录音库",
          trigger: "change"
        }
      ];

      // 当选择添加至后续跟进录音库时，需要校验录音标签
      if (formData.addToFollowUp === "是") {
        baseRules.recordingTag = [
          { required: true, message: "请选择录音标签", trigger: "change" }
        ];
      }
    }
  }

  return baseRules;
});

// 表单引用
const formRef = ref();

// 提交状态
const submitting = ref(false);

// 计算属性：是否显示后续跟进录音选项
const showFollowUpOption = computed(() => {
  // 当复检结果为【不通过】，且机器人评级结果为【A级】时展示该配置选项
  return (
    formData.reviewResult === "不通过" &&
    props.rowData?.aiAppraise?.score === "A"
  );
});

// 计算属性：是否已经添加过
const isAlreadyAdded = computed(() => {
  // 根据showPage字段判断是否已经添加过案例
  // showPage: 0 = 没有进入任何案例, 1 = 首通优秀录音库, 2 = 后续跟进录音库
  return props.rowData?.showPage && props.rowData.showPage > 0;
});

// 定义props
const props = defineProps<{
  rowData?: any;
}>();

// 定义emits
const emit = defineEmits<{
  (e: "confirm", data: any): void;
}>();

// 关闭对话框
const handleClose = () => {
  if (submitting.value) return;
  visible.value = false;
};

// 确认提交
const handleConfirm = async () => {
  if (!props.rowData || !props.rowData.id) {
    ElMessage.error("案例数据无效");
    return;
  }

  // 表单校验
  try {
    await formRef.value?.validate();
  } catch (error) {
    console.log("表单校验失败", error);
    return;
  }

  // 检查重复添加
  // if (formData.addToFollowUp === "是" && isAlreadyAdded.value) {
  //   ElMessage.warning("当前案例已添加，请勿重复添加~");
  //   return;
  // }

  submitting.value = true;
  try {
    // 构建更新参数
    const params: any = {
      id: props.rowData.id,
      actionId: props.rowData.actionId,
      humanAppraise: {
        reason: formData.reviewResult
      }
    };

    // 只有当复检结果为"不通过"时，才添加评级和原因
    if (formData.reviewResult === "不通过") {
      params.humanAppraise.score = formData.reviewGrade;
      params.humanAppraise.result = formData.failReason;
    }

    // 如果选择添加至后续跟进录音库，添加相关参数
    if (formData.addToFollowUp === "是") {
      params.followUpRecording = true;
      params.audioTags = formData.recordingTag ? [formData.recordingTag] : [];

      // 调用设置案例展示页面的API，设置为后续跟进录音库
      await setExcellentCaseShowPage({
        actionId: props.rowData.actionId,
        showPage: 2, // 2表示后续跟进录音库
        audioTags: formData.recordingTag ? [formData.recordingTag] : undefined
      });
    }

    // 调用API更新案例
    await updateExcellentCase(params);

    // 回调父组件
    emit("confirm", {
      ...props.rowData,
      ...formData
    });

    ElMessage.success("提交成功");
    visible.value = false;
  } catch (error) {
    console.error("提交失败", error);
  } finally {
    submitting.value = false;
  }
};

// 监听props.rowData变化，回显数据
watch(
  () => props.rowData,
  newVal => {
    if (newVal) {
      // 回显数据到表单
      formData.reviewResult = newVal?.humanAppraise?.reason || "通过";
      formData.reviewGrade = newVal?.humanAppraise?.score || "";
      formData.failReason = newVal?.humanAppraise?.result || "";
      formData.addToFollowUp = "否";
      formData.recordingTag = "";
    }
  },
  { immediate: true }
);

// 监听复检结果变化，重置相关字段
watch(
  () => formData.reviewResult,
  newVal => {
    if (newVal !== "不通过") {
      // 当选择"通过"时，清空所有与"不通过"相关的字段
      formData.reviewGrade = "";
      formData.failReason = "";
      formData.addToFollowUp = "否";
      formData.recordingTag = "";
    }
  }
);

// 监听添加至后续跟进录音库选项变化
watch(
  () => formData.addToFollowUp,
  newVal => {
    if (newVal !== "是") {
      formData.recordingTag = "";
    }
  }
);
</script>

<style scoped lang="scss">
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
