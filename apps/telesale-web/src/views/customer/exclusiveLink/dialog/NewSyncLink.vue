<!--
 * @Date         : 2025-02-20 16:02:56
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed, ref, reactive, onMounted } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { createErcCode } from "/@/api/customer";
import { useAppStoreHook } from "/@/store/modules/app";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { useNewSyncLink } from "@telesale/shared";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import ErCodeDown from "/@/components/ErCodeDown/index.vue";
import {
  getGoodInfoApi,
  getSyncDataApi,
  createNewQrcodeApi
} from "/@/api/customer/exclusiveLink";
import { getVenueLinkGoodsListApi } from "/@/api/customer/linkSetting";

interface Props {
  value: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const {
  form,
  syncData,
  goodInfo,
  goodList,
  loading,
  changeUpgrade,
  changeGood,
  changeSchoolYear,
  handelData
} = useNewSyncLink({
  getGoodInfoApi,
  getSyncDataApi,
  getVenueLinkGoodsListApi
});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const { isStages } = storeToRefs(useUserStore());
let imgUrl = ref<string>("");
let device = useAppStoreHook().device;
const ruleFormRef = ref<FormInstance>();

const rules = reactive<FormRules>({
  schoolYear: [
    {
      required: true,
      message: "请选择年级",
      trigger: "change"
    }
  ],
  goods: [
    {
      required: true,
      message: "请选择商品",
      trigger: "change"
    }
  ],
  isInstallment: [
    {
      required: true,
      message: "请选择分期支付",
      trigger: "change"
    }
  ],
  installmentPayType: [
    {
      required: true,
      message: "请选择分期支付方式",
      trigger: "change"
    }
  ]
});

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async valid => {
    if (valid) {
      loading.value = true;
      const params = handelData();
      console.log("para,s", params);

      createNewQrcodeApi(params)
        .then(({ data }: { data: any }) => {
          let blob = new Blob([data], { type: "png" });
          imgUrl.value = (window.URL || window.webkitURL).createObjectURL(blob);
          loading.value = false;
          ElMessage.success("操作成功");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
</script>

<template>
  <el-dialog
    title="创建会场链接（新）"
    v-model="isModel"
    :before-close="handleClose"
  >
    <div v-if="imgUrl" style="text-align: center; padding-bottom: 10px">
      <ErCodeDown :imgUrl="imgUrl" />
    </div>
    <el-form
      v-else
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '180px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      :rules="rules"
      v-loading="loading"
    >
      <el-row>
        <el-col :lg="2" />
        <el-col :lg="20">
          <el-form-item prop="schoolYear" label="年级">
            <el-radio-group
              v-model="form.schoolYear"
              @change="changeSchoolYear"
            >
              <el-radio
                v-for="(item, index) in syncData"
                :key="index"
                :label="item.schoolYear"
              >
                {{
                  item.schoolYear === "三年级" ? "一到三年级" : item.schoolYear
                }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="goods" label="商品">
            <el-select
              v-model="form.goods"
              placeholder="请选择商品"
              value-key="id"
              clearable
              filterable
              @change="changeGood"
            >
              <el-option
                v-for="(item, index) in goodList"
                :key="index"
                :label="item.strategyName"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            prop="isPackGood"
            label="打包购买首购+升单商品"
            v-if="form.goods?.packGood?.length"
          >
            <el-radio-group v-model="form.isPackGood" @change="changeUpgrade">
              <el-radio :label="false">否</el-radio>
              <el-radio :label="true">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            prop="addPad"
            label="加购平板"
            v-if="form.goods?.addContent?.length"
          >
            <el-radio-group v-model="form.addPad" @change="changeUpgrade">
              <el-radio
                :label="item.label"
                v-for="item in form.goods?.addContent"
                :key="item.label"
              >
                {{ item.name }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="goodInfo">
            <el-form-item label="商品原价">
              ¥ {{ goodInfo.originalAmount?.toFixed(2) }}
            </el-form-item>
            <el-form-item label="商品售价">
              ¥ {{ goodInfo.sellAmount?.toFixed(2) }}
            </el-form-item>
            <el-form-item label="用户实付价">
              ¥ {{ goodInfo.amount?.toFixed(2) }}
            </el-form-item>
          </template>

          <template v-if="isStages">
            <el-form-item label="分期支付" prop="isInstallment">
              <el-radio-group
                v-model="form.isInstallment"
                @change="changeInstallment"
              >
                <el-radio
                  v-for="(item, index) in isStagesList"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              label="分期支付方式"
              prop="installmentPayType"
              v-if="form.isInstallment === 1"
            >
              <el-checkbox-group v-model="form.installmentPayType">
                <el-checkbox
                  v-for="(item, index) in stagesType"
                  :key="index"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </template>
        </el-col>
        <el-col :lg="2" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)" v-if="!imgUrl">
        生成二维码
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
