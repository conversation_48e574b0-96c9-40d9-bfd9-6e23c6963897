<script setup lang="tsx">
import { ref } from "vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { Column } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { TableColumns } from "/@/components/ReTable/types";
import { phoneTypeMap } from "/@/utils/const/customer";
import { getFamilyOrderApi } from "/@/api/customer/familyNumber";
import ShipmentsListModal from "../../dialog/ShipmentsModal.vue";
import { getUserOrderApi } from "@telesale/server/src/api/order/index";
import dayjs from "dayjs";
import timeChange from "/@/utils/handle/timeChange";
import { getAuth } from "/@/utils/auth";

let device = useAppStoreHook().device;
interface Props {
  userid?: string;
  familyId?: string;
}

const props = defineProps<Props>();

const loading = ref(false);
const dataList = ref([]);
const isShipmentsModal = ref<boolean>(false);
const rowId = ref();
const initData = ref([]);

const statusList = [
  {
    label: "等待支付",
    value: "等待支付"
  },
  {
    label: "支付超时",
    value: "支付超时"
  },
  {
    label: "订单关闭",
    value: "订单关闭"
  },
  {
    label: "支付成功",
    value: "支付成功"
  },
  {
    label: "退款成功",
    value: "退款成功"
  },
  {
    label: "分享订单",
    value: "分享订单"
  },
  {
    label: "创建订单",
    value: "创建订单"
  }
];

const listHeader: TableColumns[] = [
  { field: "_id", desc: "订单号", minWidth: 135 },
  { field: "onionId", desc: "洋葱ID", isShow: !!props.familyId, minWidth: 100 },
  {
    field: "phone",
    desc: "客户手机号",
    isShow: !!props.familyId,
    minWidth: 120
  },
  {
    field: "phoneType",
    desc: "手机号类型",
    isShow: !!props.familyId,
    minWidth: 130,
    customRender: ({ text }) => {
      return phoneTypeMap[text];
    }
  },
  {
    field: "name",
    desc: "商品名称",
    minWidth: 160,
    slot: {
      name: "goodName"
    }
  },
  { field: "createdAt", desc: "创建时间", timeChange: 3, minWidth: 135 },
  {
    field: "paidTime",
    desc: "支付成功时间",
    minWidth: 135,
    customRender: ({ text }) => {
      return timeChange(text, 3) || "--";
    }
  },
  {
    field: "authEndTime",
    desc: "订单权益到期时间",
    headerTipTitle: "若订单中存在未使用权益，此截止时间可能不准确",
    headerTip: true,
    minWidth: 135,
    isShow: () => getAuth("telesale_admin_custom_order_authEndTime"),
    customRender: ({ row, text }) => {
      return row.authUsage === 2 ? "权益暂未使用" : timeChange(text, 3) || "--";
    }
  },
  {
    field: "status",
    desc: "订单状态",
    filterOptions: {
      columns: statusList,
      isMultiple: true
    }
  },
  {
    field: "originalAmount",
    desc: "订单总价",
    filters: row => row.good.amount
  },
  { field: "amount", desc: "实付价格" },
  { field: "paymentPlatform", desc: "支付平台" }
];

const columns: Column[] = [
  {
    key: `name`,
    dataKey: `name`,
    title: `名称`,
    width: 324,
    align: "center",
    cellRenderer: ({ rowData }) => {
      return rowData.sku.name;
    },
    headerCellRenderer: () => {
      return <h2 class="font-bold text-16px">商品类目规格</h2>;
    }
  }
];

let operation =
  useUserStoreHook().authorizationMap.indexOf(
    "telesale_admin_custom_logistics"
  ) > -1
    ? [
        {
          event: "details",
          text: "查看物流",
          isShow: row => row.status === "支付成功" || row.status === "退款成功"
        }
      ]
    : [];

function details(row) {
  console.log(row);

  rowId.value = row._id;
  isShipmentsModal.value = true;
}

function parantMath({ key, params }) {
  switch (key) {
    case "details":
      details(params);
      break;
  }
}

const getHeight = (index: number) => {
  const list = [100, 150, 200];
  return `${list[index - 1]}px`;
};

function getList() {
  loading.value = true;
  if (props.userid) {
    getUserOrderApi({ userId: props.userid })
      .then(({ data }: { data: any }) => {
        dataList.value = data;
        initData.value = data;
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
  }

  if (props.familyId) {
    getFamilyOrderApi({ familyId: props.familyId })
      .then(res => {
        const data = res.data.familyOrderInfos;
        data.forEach(item => {
          const order = item.order;
          Object.assign(item, order);
        });
        dataList.value = data;
        initData.value = data;
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

const filterHeadData = (filterData: Record<string, any>) => {
  if (filterData.status?.length) {
    dataList.value = initData.value.filter(item =>
      filterData.status?.includes(item.status)
    );
  } else {
    dataList.value = initData.value;
  }
};

getList();
</script>

<template>
  <div v-loading="loading">
    <template v-if="device !== 'mobile'">
      <ReTable
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        @parantMath="parantMath"
        @filterHeadData="filterHeadData"
      >
        <template #goodName="{ row }">
          <div class="flex items-center gap-5px">
            <span>{{ row.good.name }}</span>
            <el-popover
              placement="top"
              :width="350"
              style="height: 400px"
              trigger="hover"
            >
              <template #reference>
                <el-icon class="cursor-pointer"><InfoFilled /></el-icon>
              </template>
              <div
                :style="[
                  row.good.skuList.length > 3
                    ? 'height: 300px'
                    : `height: ${getHeight(row.good.skuList.length)}`
                ]"
              >
                <el-auto-resizer>
                  <template #default="{ height, width }">
                    <el-table-v2
                      :columns="columns"
                      :data="row.good.skuList"
                      :width="width"
                      :height="height"
                      fixed
                    />
                  </template>
                </el-auto-resizer>
              </div>
            </el-popover>
          </div>
        </template>
      </ReTable>
    </template>
    <template v-else>
      <ReCardList
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        @parantMath="parantMath"
        :isCardBox="false"
      >
        <template #goodName="{ row }">
          <div class="flex items-center gap-5px">
            <span>{{ row.good.name }}</span>
            <el-popover
              placement="top"
              :width="350"
              style="height: 400px"
              trigger="hover"
            >
              <template #reference>
                <el-icon class="cursor-pointer"><InfoFilled /></el-icon>
              </template>
              <div
                :style="[
                  row.good.skuList.length > 3
                    ? 'height: 300px'
                    : `height: ${getHeight(row.good.skuList.length)}`
                ]"
              >
                <el-auto-resizer>
                  <template #default="{ height, width }">
                    <el-table-v2
                      :columns="columns"
                      :data="row.good.skuList"
                      :width="width"
                      :height="height"
                      fixed
                    />
                  </template>
                </el-auto-resizer>
              </div>
            </el-popover>
          </div>
        </template>
      </ReCardList>
    </template>
    <ShipmentsListModal
      v-if="isShipmentsModal"
      v-model:value="isShipmentsModal"
      :id="rowId"
    />
  </div>
</template>
