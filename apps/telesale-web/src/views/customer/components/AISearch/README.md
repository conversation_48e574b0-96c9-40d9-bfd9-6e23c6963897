# AI搜索组件 (AISearch)

一个完整的AI搜索解决方案，包含悬浮图标和自定义对话框，无遮罩层设计。

## 🎯 功能特性

- **悬浮图标**：圆形悬浮图标，支持点击打开对话框
- **自定义对话框**：无遮罩层的悬浮对话框设计
- **AI对话**：左右布局的对话界面
- **智能交互**：支持重新解析和条件确认
- **响应式设计**：适配桌面和移动端
- **无依赖Element Dialog**：完全自定义样式

## 📁 文件结构

```
src/components/AISearch/
├── index.vue              # 主组件（悬浮图标 + 对话框）
├── AiChatDialog.vue       # 对话框组件
├── AiSearchExample.vue    # 使用示例
└── README.md             # 说明文档
```

## 🚀 使用方法

### 基础使用

```vue
<template>
  <div class="page-container">
    <!-- 页面内容 -->
    <div class="content">
      <!-- 你的页面内容 -->
    </div>
    
    <!-- AI搜索组件 -->
    <AISearch />
  </div>
</template>

<script setup>
import AISearch from '@/components/AISearch/index.vue'
</script>
```

### 高级配置

```vue
<template>
  <AISearch 
    @send-message="handleSendMessage"
    @reparse="handleReparse"
    @confirm-filters="handleConfirmFilters"
  />
</template>

<script setup>
import AISearch from '@/components/AISearch/index.vue'

const handleSendMessage = (message) => {
  console.log('用户发送:', message)
  // 处理发送消息逻辑
}

const handleReparse = (message) => {
  console.log('重新解析:', message)
  // 处理重新解析逻辑
}

const handleConfirmFilters = () => {
  console.log('确认筛选条件')
  // 处理确认筛选逻辑
}
</script>
```

## 🎨 设计特点

### 悬浮图标
- **位置**：固定在页面右上角
- **样式**：50px圆形，白色背景，阴影效果
- **交互**：悬停放大，点击缩小动画

### 对话框样式
- **无遮罩层**：不影响页面其他操作
- **悬浮设计**：固定在右上角，400x500px
- **圆角边框**：12px圆角，现代化设计
- **阴影效果**：8px模糊阴影，提升层次感

### 对话布局
- **AI消息**：左侧，灰色气泡，带头像
- **用户消息**：右侧，蓝色气泡
- **确认按钮**：AI消息下方，主次按钮区分

## 📱 响应式适配

### 桌面端 (>768px)
- 对话框：400x500px
- 悬浮图标：50x50px
- 位置：右上角固定

### 平板端 (≤768px)
- 对话框：350x450px
- 悬浮图标：45x45px
- 位置：适当调整边距

### 移动端 (≤480px)
- 对话框：全宽度，高度400px
- 悬浮图标：45x45px
- 位置：左右边距10px

## 🎪 Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| send-message | 用户发送消息 | (message: string) |
| reparse | 重新解析消息 | (message: string) |
| confirm-filters | 确认筛选条件 | () |

## 🔧 API 集成

### 当前API
- `mockAiSearchResponse` - 模拟AI回复
- `mockFilterResults` - 模拟筛选结果

### 切换真实API
在 `AiChatDialog.vue` 中替换：

```typescript
// 发送消息
const response = await sendAiSearchMessage({
  message: userMessage,
  sessionId: sessionId.value
})

// 重新解析
const response = await reparseAiMessage({
  message: lastUserMessage.value,
  sessionId: sessionId.value
})

// 确认筛选
const response = await confirmFiltersAndSearch({
  sessionId: sessionId.value,
  filters: confirmationData
})
```

## 🎯 样式定制

### 修改悬浮图标位置

```scss
.ai-search-floating {
  top: 60px;    // 距离顶部
  right: 30px;  // 距离右侧
  left: auto;   // 改为左侧：left: 30px; right: auto;
}
```

### 修改对话框尺寸

```scss
.ai-chat-floating {
  width: 450px;   // 宽度
  height: 600px;  // 高度
  top: 100px;     // 距离顶部
}
```

### 修改主题颜色

```scss
// 主色调
$primary-color: #409eff;

// 用户消息气泡
.user-bubble {
  background-color: $primary-color;
}

// 发送按钮
.send-button {
  background: $primary-color;
}
```

## 🚨 注意事项

1. **z-index层级**：对话框z-index为2000，图标为1000
2. **无遮罩设计**：用户可以同时操作页面和对话框
3. **自动滚动**：新消息自动滚动到底部
4. **键盘支持**：Enter发送，Shift+Enter换行
5. **错误处理**：包含完善的错误提示机制

## 📦 依赖说明

- Vue 3 Composition API
- Element Plus (仅用于消息提示)
- 自定义API接口 (`/@/api/customer/aiSearch`)

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持悬浮图标和对话框
- 无遮罩层设计
- 响应式适配
- API集成支持
