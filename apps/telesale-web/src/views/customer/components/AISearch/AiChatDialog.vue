<!--
 * @Date         : 2025-07-14 17:00:00
 * @Description  : AI对话悬浮组件 - 自定义样式，无遮罩层
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  mockAiSearchResponse,
  mockFilterResults
} from "/@/api/customer/aiSearch";

interface Message {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: number;
  isConfirmation?: boolean;
  confirmationData?: {
    filters: string;
    timeRange: string;
    intention: string;
  };
}

interface Props {
  visible: boolean;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "send-message", message: string): void;
  (e: "reparse", message: string): void;
  (e: "confirm-filters"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

const messages = ref<Message[]>([
  {
    id: "1",
    content: "建议搜索话术",
    isBot: true,
    timestamp: Date.now()
  }
]);

const inputMessage = ref("");
const chatContentRef = ref<HTMLElement>();
const loading = ref(false);
const sessionId = ref("");
const lastUserMessage = ref("");

// 添加消息
const addMessage = (
  content: string,
  isBot: boolean,
  isConfirmation = false,
  confirmationData?: any
) => {
  const message: Message = {
    id: Date.now().toString(),
    content,
    isBot,
    timestamp: Date.now(),
    isConfirmation,
    confirmationData
  };
  messages.value.push(message);
  scrollToBottom();
};

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || loading.value) return;

  const userMessage = inputMessage.value.trim();
  lastUserMessage.value = userMessage;
  addMessage(userMessage, false);

  // 清空输入框
  inputMessage.value = "";

  // 显示加载状态
  loading.value = true;

  try {
    // 调用AI搜索API（目前使用模拟API）
    const response = await mockAiSearchResponse(userMessage);

    // 更新会话ID
    if (response.sessionId) {
      sessionId.value = response.sessionId;
    }

    // 添加AI回复
    addMessage(
      response.message,
      true,
      response.isConfirmation,
      response.confirmationData
    );

    // 发送到父组件
    emit("send-message", userMessage);
  } catch (error) {
    console.error("发送消息失败:", error);
    ElMessage.error("发送消息失败，请重试");
    addMessage("抱歉，我遇到了一些问题，请稍后重试。", true);
  } finally {
    loading.value = false;
  }
};

// 重新解析
const handleReparse = async () => {
  if (lastUserMessage.value) {
    loading.value = true;

    try {
      // 调用重新解析API
      const response = await mockAiSearchResponse(lastUserMessage.value);

      // 添加重新解析的结果
      addMessage(
        response.message,
        true,
        response.isConfirmation,
        response.confirmationData
      );

      emit("reparse", lastUserMessage.value);
    } catch (error) {
      console.error("重新解析失败:", error);
      ElMessage.error("重新解析失败，请重试");
      addMessage("重新解析失败，请稍后重试。", true);
    } finally {
      loading.value = false;
    }
  }
};

// 确认筛选条件
const handleConfirm = async () => {
  loading.value = true;

  try {
    // 调用确认筛选条件API
    const response = await mockFilterResults();

    addMessage(
      `筛选条件已确认，为您找到 ${response.total} 条符合条件的记录。`,
      true
    );

    emit("confirm-filters");
  } catch (error) {
    console.error("确认筛选条件失败:", error);
    ElMessage.error("确认筛选条件失败，请重试");
    addMessage("确认筛选条件失败，请稍后重试。", true);
  } finally {
    loading.value = false;
  }
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight;
    }
  });
};

// 关闭对话框
const handleClose = () => {
  isVisible.value = false;
};

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};
</script>

<template>
  <!-- 悬浮对话框 - 无遮罩层 -->
  <div v-if="isVisible" class="ai-chat-floating">
    <!-- 对话框头部 -->
    <div class="chat-header">
      <div class="header-title">
        <span class="title-text">AI搜索</span>
      </div>
      <button class="close-button" @click="handleClose">×</button>
    </div>

    <!-- 对话内容区域 -->
    <div ref="chatContentRef" class="chat-content">
      <div class="message-list">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'message-item',
            message.isBot ? 'bot-message' : 'user-message'
          ]"
        >
          <!-- AI消息 -->
          <div v-if="message.isBot" class="bot-message-wrapper">
            <div class="bot-avatar">
              <img src="/@/assets/favicon.ico" alt="AI" />
            </div>
            <div class="message-bubble bot-bubble">
              <div class="message-content">{{ message.content }}</div>

              <!-- 确认按钮区域 -->
              <div v-if="message.isConfirmation" class="confirmation-buttons">
                <button
                  class="action-button secondary"
                  @click="handleReparse"
                  :disabled="loading"
                >
                  重新解析
                </button>
                <button
                  class="action-button primary"
                  @click="handleConfirm"
                  :disabled="loading"
                >
                  确认
                </button>
              </div>
            </div>
          </div>

          <!-- 用户消息 -->
          <div v-else class="user-message-wrapper">
            <div class="message-bubble user-bubble">
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-wrapper">
        <textarea
          v-model="inputMessage"
          placeholder="请输入您想查询的内容"
          class="message-input"
          rows="1"
          @keydown="handleKeydown"
          :disabled="loading"
        ></textarea>
        <button
          class="send-button"
          @click="sendMessage"
          :disabled="!inputMessage.trim() || loading"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M2 21L23 12L2 3V10L17 12L2 14V21Z"
              fill="currentColor"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ai-chat-floating {
  position: fixed;
  top: 80px;
  right: 20px;
  width: 400px;
  height: 500px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e4e7ed;
  z-index: 2000;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 头部样式
  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background: #fafafa;

    .header-title {
      .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }
    }

    .close-button {
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      font-size: 18px;
      color: #909399;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: all 0.2s;

      &:hover {
        background: #f5f7fa;
        color: #606266;
      }
    }
  }

  // 对话内容区域
  .chat-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #f8f9fa;

    .message-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .message-item {
      display: flex;
      width: 100%;
    }

    .bot-message-wrapper {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      justify-content: flex-start;
      margin-right: 20%;
    }

    .user-message-wrapper {
      display: flex;
      justify-content: flex-end;
      margin-left: 20%;
    }

    .bot-avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .message-bubble {
      max-width: 70%;
      padding: 10px 14px;
      border-radius: 12px;
      word-wrap: break-word;
      line-height: 1.4;
      font-size: 14px;
    }



    .bot-bubble {
      max-width: 75%;
      background-color: #e4e6ea;
      color: #333;
      border-bottom-left-radius: 4px;
    }

    .user-bubble {
      max-width: 65%;
      background-color: #409eff;
      color: white;
      border-bottom-right-radius: 4px;
      text-align: right;
    }

    .message-content {
      white-space: pre-wrap;
      text-align: inherit;
    }

    .confirmation-buttons {
      margin-top: 10px;
      display: flex;
      gap: 8px;

      .action-button {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;

        &.primary {
          background: #409eff;
          color: white;

          &:hover:not(:disabled) {
            background: #66b1ff;
          }
        }

        &.secondary {
          background: #f4f4f5;
          color: #606266;

          &:hover:not(:disabled) {
            background: #e9e9eb;
          }
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }
  }

  // 输入区域
  .input-area {
    padding: 12px 16px;
    border-top: 1px solid #ebeef5;
    background: #ffffff;

    .input-wrapper {
      display: flex;
      gap: 8px;
      align-items: flex-end;

      .message-input {
        flex: 1;
        min-height: 36px;
        max-height: 80px;
        padding: 8px 12px;
        border: 1px solid #dcdfe6;
        border-radius: 8px;
        font-size: 14px;
        line-height: 1.4;
        resize: none;
        outline: none;
        transition: border-color 0.2s;

        &:focus {
          border-color: #409eff;
        }

        &::placeholder {
          color: #c0c4cc;
        }

        &:disabled {
          background: #f5f7fa;
          color: #c0c4cc;
        }
      }

      .send-button {
        width: 36px;
        height: 36px;
        background: #409eff;
        border: none;
        border-radius: 8px;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;

        &:hover:not(:disabled) {
          background: #66b1ff;
        }

        &:disabled {
          background: #c0c4cc;
          cursor: not-allowed;
        }
      }
    }
  }

  // 滚动条样式
  .chat-content::-webkit-scrollbar {
    width: 4px;
  }

  .chat-content::-webkit-scrollbar-track {
    background: transparent;
  }

  .chat-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
  }

  .chat-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .ai-chat-floating {
    width: 350px;
    height: 450px;
    top: 60px;
    right: 10px;
  }
}

@media (max-width: 480px) {
  .ai-chat-floating {
    width: calc(100vw - 20px);
    height: 400px;
    top: 50px;
    right: 10px;
    left: 10px;
  }
}
</style>
