# AI对话组件 (AiChatDialog)

一个完整的AI搜索对话界面组件，支持智能搜索、条件确认和重新解析功能。

## 🎯 功能特性

- **智能对话**：AI与用户的自然语言交互
- **条件确认**：AI解析用户需求后提供确认界面
- **重新解析**：支持重新解析用户的搜索需求
- **实时API**：集成后端API接口调用
- **错误处理**：完善的错误处理和用户提示
- **响应式设计**：适配不同屏幕尺寸

## 🚀 使用方法

### 基础使用

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <el-button @click="showDialog = true">打开AI搜索</el-button>
    
    <!-- AI对话组件 -->
    <AiChatDialog
      v-model:visible="showDialog"
      @send-message="handleSendMessage"
      @reparse="handleReparse"
      @confirm-filters="handleConfirmFilters"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AiChatDialog from './AiChatDialog.vue'

const showDialog = ref(false)

const handleSendMessage = (message) => {
  console.log('用户发送消息:', message)
}

const handleReparse = (message) => {
  console.log('重新解析消息:', message)
}

const handleConfirmFilters = () => {
  console.log('确认筛选条件')
}
</script>
```

### 与悬浮图标组合使用

```vue
<template>
  <div>
    <!-- 悬浮AI搜索图标 -->
    <AiSearch />
  </div>
</template>

<script setup>
import AiSearch from './AiSearch/index.vue'
</script>
```

## 📋 Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | false | 控制对话框显示/隐藏 |

## 🎪 Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:visible | 对话框显示状态变化 | (visible: boolean) |
| send-message | 用户发送消息 | (message: string) |
| reparse | 重新解析消息 | (message: string) |
| confirm-filters | 确认筛选条件 | () |

## 🎨 界面设计

### 对话布局
- **左侧**：AI消息（灰色气泡）
- **右侧**：用户消息（蓝色气泡）
- **AI头像**：显示在AI消息左侧
- **确认按钮**：在需要确认的AI消息下方

### 交互流程
1. 用户输入搜索需求
2. AI解析并返回确认信息
3. 用户可选择"重新解析"或"确认"
4. 确认后返回搜索结果

## 🔧 API 集成

### 当前使用的API

```typescript
// 发送消息
import { mockAiSearchResponse } from '/@/api/customer/aiSearch'

// 重新解析
import { mockAiSearchResponse } from '/@/api/customer/aiSearch'

// 确认筛选
import { mockFilterResults } from '/@/api/customer/aiSearch'
```

### 切换到真实API

将模拟API替换为真实API：

```typescript
// 替换 mockAiSearchResponse 为 sendAiSearchMessage
const response = await sendAiSearchMessage({
  message: userMessage,
  sessionId: sessionId.value
})

// 替换 mockFilterResults 为 confirmFiltersAndSearch
const response = await confirmFiltersAndSearch({
  sessionId: sessionId.value,
  filters: confirmationData
})
```

## 🎯 使用场景

1. **客户搜索**：智能搜索客户信息
2. **数据筛选**：通过自然语言筛选数据
3. **条件确认**：确保搜索条件准确性
4. **快速查询**：提供便捷的查询入口

## 📱 响应式适配

- **桌面端**：600px 宽度对话框
- **移动端**：自动适配屏幕宽度
- **滚动条**：自定义样式，美观实用

## 🔍 样式定制

```scss
// 自定义对话框样式
.ai-chat-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
  }
  
  // 自定义消息气泡颜色
  .bot-bubble {
    background-color: #f0f0f0;
  }
  
  .user-bubble {
    background-color: #007bff;
  }
}
```

## 🚨 注意事项

1. **API配置**：确保后端API接口正确配置
2. **错误处理**：组件已包含完善的错误处理
3. **会话管理**：支持会话ID管理，保持对话连续性
4. **性能优化**：大量消息时建议添加虚拟滚动

## 📁 相关文件

- `AiChatDialog.vue` - 主对话组件
- `AiSearch/index.vue` - 悬浮图标组件
- `aiSearch.ts` - API接口定义
- `AiSearchExample.vue` - 使用示例
