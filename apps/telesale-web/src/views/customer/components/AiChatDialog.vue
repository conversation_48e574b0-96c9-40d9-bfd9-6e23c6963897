<!--
 * @Date         : 2025-07-14 16:30:00
 * @Description  : AI对话组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  sendAiSearchMessage,
  reparseAiMessage,
  confirmFiltersAndSearch,
  mockAiSearchResponse,
  mockFilterResults,
  type AiSearchMessage
} from "/@/api/customer/aiSearch";

interface Message {
  id: string;
  content: string;
  isBot: boolean;
  timestamp: number;
  isConfirmation?: boolean;
  confirmationData?: {
    filters: string;
    timeRange: string;
    intention: string;
  };
}

interface Props {
  visible: boolean;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (e: "send-message", message: string): void;
  (e: "reparse", message: string): void;
  (e: "confirm-filters"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const isVisible = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

const messages = ref<Message[]>([
  {
    id: "1",
    content: "建议搜索话术",
    isBot: true,
    timestamp: Date.now()
  }
]);

const inputMessage = ref("");
const chatContentRef = ref<HTMLElement>();
const loading = ref(false);
const sessionId = ref("");
const lastUserMessage = ref("");

// 添加消息
const addMessage = (
  content: string,
  isBot: boolean,
  isConfirmation = false,
  confirmationData?: any
) => {
  const message: Message = {
    id: Date.now().toString(),
    content,
    isBot,
    timestamp: Date.now(),
    isConfirmation,
    confirmationData
  };
  messages.value.push(message);
  scrollToBottom();
};

// 发送消息
const sendMessage = async () => {
  if (!inputMessage.value.trim() || loading.value) return;

  const userMessage = inputMessage.value.trim();
  lastUserMessage.value = userMessage;
  addMessage(userMessage, false);

  // 清空输入框
  inputMessage.value = "";

  // 显示加载状态
  loading.value = true;

  try {
    // 调用AI搜索API（目前使用模拟API）
    const response = await mockAiSearchResponse(userMessage);

    // 更新会话ID
    if (response.sessionId) {
      sessionId.value = response.sessionId;
    }

    // 添加AI回复
    addMessage(
      response.message,
      true,
      response.isConfirmation,
      response.confirmationData
    );

    // 发送到父组件
    emit("send-message", userMessage);
  } catch (error) {
    console.error("发送消息失败:", error);
    ElMessage.error("发送消息失败，请重试");
    addMessage("抱歉，我遇到了一些问题，请稍后重试。", true);
  } finally {
    loading.value = false;
  }
};

// 重新解析
const handleReparse = async () => {
  if (lastUserMessage.value) {
    loading.value = true;

    try {
      // 调用重新解析API
      const response = await mockAiSearchResponse(lastUserMessage.value);

      // 添加重新解析的结果
      addMessage(
        response.message,
        true,
        response.isConfirmation,
        response.confirmationData
      );

      emit("reparse", lastUserMessage.value);
    } catch (error) {
      console.error("重新解析失败:", error);
      ElMessage.error("重新解析失败，请重试");
      addMessage("重新解析失败，请稍后重试。", true);
    } finally {
      loading.value = false;
    }
  }
};

// 确认筛选条件
const handleConfirm = async () => {
  loading.value = true;

  try {
    // 调用确认筛选条件API
    const response = await mockFilterResults();

    addMessage(
      `筛选条件已确认，为您找到 ${response.total} 条符合条件的记录。`,
      true
    );

    emit("confirm-filters");
  } catch (error) {
    console.error("确认筛选条件失败:", error);
    ElMessage.error("确认筛选条件失败，请重试");
    addMessage("确认筛选条件失败，请稍后重试。", true);
  } finally {
    loading.value = false;
  }
};

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight;
    }
  });
};

// 关闭对话框
const handleClose = () => {
  isVisible.value = false;
};

// 键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};
</script>

<template>
  <el-dialog
    v-model="isVisible"
    title="AI搜索"
    width="600px"
    :before-close="handleClose"
    class="ai-chat-dialog"
  >
    <template #header>
      <div class="flex justify-between items-center">
        <span class="text-18px font-bold">AI搜索</span>
        <el-button
          type="text"
          @click="handleClose"
          class="text-20px p-0 w-30px h-30px"
        >
          ×
        </el-button>
      </div>
    </template>

    <!-- 对话内容区域 -->
    <div ref="chatContentRef" class="chat-content">
      <div class="message-list">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'message-item',
            message.isBot ? 'bot-message' : 'user-message'
          ]"
        >
          <!-- AI消息 -->
          <div v-if="message.isBot" class="bot-message-wrapper">
            <div class="bot-avatar">
              <img src="/@/assets/favicon.ico" alt="AI" />
            </div>
            <div class="message-bubble bot-bubble">
              <div class="message-content">{{ message.content }}</div>

              <!-- 确认按钮区域 -->
              <div v-if="message.isConfirmation" class="confirmation-buttons">
                <el-button
                  size="small"
                  @click="handleReparse"
                  :loading="loading"
                >
                  重新解析
                </el-button>
                <el-button type="primary" size="small" @click="handleConfirm">
                  确认
                </el-button>
              </div>
            </div>
          </div>

          <!-- 用户消息 -->
          <div v-else class="user-message-wrapper">
            <div class="message-bubble user-bubble">
              <div class="message-content">{{ message.content }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <template #footer>
      <div class="input-area">
        <div class="input-wrapper">
          <el-input
            v-model="inputMessage"
            placeholder="请输入您想查询的内容"
            type="textarea"
            :rows="1"
            resize="none"
            @keydown="handleKeydown"
            :disabled="loading"
          />
          <el-button
            type="primary"
            @click="sendMessage"
            :loading="loading"
            :disabled="!inputMessage.trim()"
            class="send-button"
          >
            <i class="el-icon-position" />
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.ai-chat-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
    height: 400px;
    display: flex;
    flex-direction: column;
  }

  :deep(.el-dialog__footer) {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #f5f7fa;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-item {
  display: flex;
  width: 100%;
}

.bot-message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  justify-content: flex-start;
}

.user-message-wrapper {
  display: flex;
  justify-content: flex-end;
}

.bot-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  word-wrap: break-word;
  line-height: 1.4;
}

.bot-bubble {
  background-color: #e4e6ea;
  color: #333;
  border-bottom-left-radius: 4px;
}

.user-bubble {
  background-color: #409eff;
  color: white;
  border-bottom-right-radius: 4px;
}

.message-content {
  white-space: pre-wrap;
}

.confirmation-buttons {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.input-area {
  .input-wrapper {
    display: flex;
    gap: 10px;
    align-items: flex-end;

    :deep(.el-textarea) {
      flex: 1;
    }

    .send-button {
      height: 32px;
      width: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 滚动条样式
.chat-content::-webkit-scrollbar {
  width: 6px;
}

.chat-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.chat-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
