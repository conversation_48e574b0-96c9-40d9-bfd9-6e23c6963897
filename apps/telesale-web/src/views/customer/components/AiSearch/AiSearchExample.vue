<!--
 * @Date         : 2025-07-14 17:15:00
 * @Description  : AI搜索组件使用示例
 * @Autor        : xia<PERSON>hen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import AISearch from "./index.vue";

// 示例数据
const customerData = ref([
  {
    id: 1,
    name: "张三",
    phone: "13800138001",
    intention: "意向",
    lastViewTime: "2025-05-26 10:30:00"
  },
  {
    id: 2,
    name: "李四",
    phone: "13800138002",
    intention: "无意向",
    lastViewTime: "2025-05-25 14:20:00"
  },
  {
    id: 3,
    name: "王五",
    phone: "13800138003",
    intention: "意向",
    lastViewTime: "2025-05-26 16:45:00"
  }
]);

const filteredData = ref(customerData.value);

// 模拟筛选功能
const applyFilters = (filters: any) => {
  console.log("应用筛选条件:", filters);
  // 这里可以根据筛选条件过滤数据
  // filteredData.value = customerData.value.filter(...)
};
</script>

<template>
  <div class="ai-search-example">
    <div class="page-header">
      <h1>客户管理页面</h1>
      <p>这是一个示例页面，展示AI搜索组件的使用</p>
    </div>

    <!-- 页面内容 -->
    <div class="page-content">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>客户列表</span>
          </div>
        </template>

        <el-table :data="filteredData" style="width: 100%">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="姓名" width="120" />
          <el-table-column prop="phone" label="电话" width="150" />
          <el-table-column prop="intention" label="意向度" width="100" />
          <el-table-column prop="lastViewTime" label="最近看课时间" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button size="small" type="primary">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- AI搜索组件 - 悬浮在页面右上角 -->
    <AISearch />
  </div>
</template>

<style lang="scss" scoped>
.ai-search-example {
  position: relative;
  min-height: 100vh;
  padding: 20px;
  background-color: #f5f7fa;

  .page-header {
    margin-bottom: 20px;

    h1 {
      margin: 0 0 8px 0;
      font-size: 24px;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .page-content {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}
</style>
