<!--
 * @Date         : 2025-07-14 17:10:00
 * @Description  : AI搜索悬浮图标和对话组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import AiChatDialog from "./AiChatDialog.vue";

const showChatDialog = ref(false);

// 打开AI对话框
const openChatDialog = () => {
  showChatDialog.value = true;
};

// 处理发送消息
const handleSendMessage = (message: string) => {
  console.log("发送消息:", message);
  // 这里调用后端API发送消息
};

// 处理重新解析
const handleReparse = (message: string) => {
  console.log("重新解析:", message);
  // 这里调用后端API重新解析
};

// 处理确认筛选条件
const handleConfirmFilters = () => {
  console.log("确认筛选条件");
  // 这里调用后端API确认筛选条件
};
</script>

<template>
  <div class="ai-search-container">
    <!-- 悬浮图标 -->
    <div
      class="ai-search-floating"
      @click="openChatDialog"
      v-if="!showChatDialog"
    >
      <img src="/@/assets/favicon.ico" alt="AI搜索" class="ai-search-icon" />
    </div>

    <!-- AI对话框 -->
    <AiChatDialog
      v-model:visible="showChatDialog"
      @send-message="handleSendMessage"
      @reparse="handleReparse"
      @confirm-filters="handleConfirmFilters"
    />
  </div>
</template>

<style lang="scss" scoped>
.ai-search-container {
  position: relative;
}

.ai-search-floating {
  position: absolute;
  top: -12px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  // 悬停效果
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  // 点击效果
  &:active {
    transform: scale(0.95);
  }

  .ai-search-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .ai-search-floating {
    width: 45px;
    height: 45px;
    top: 10px;
    right: 15px;

    .ai-search-icon {
      width: 25px;
      height: 25px;
    }
  }
}
</style>
