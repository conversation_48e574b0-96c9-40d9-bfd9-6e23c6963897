<!--
 * @Date         : 2025-07-14 16:05:25
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup></script>

<template>
  <div class="ai-search-floating">
    <img src="/@/assets/favicon.ico" alt="AI搜索" class="ai-search-icon" />
  </div>
</template>

<style lang="scss" scoped>
.ai-search-floating {
  position: fixed;
  top: 40px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #ffffff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  // 悬停效果
  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }

  // 点击效果
  &:active {
    transform: scale(0.95);
  }

  .ai-search-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .ai-search-floating {
    width: 45px;
    height: 45px;
    top: 30px;
    right: 15px;

    .ai-search-icon {
      width: 25px;
      height: 25px;
    }
  }
}
</style>
