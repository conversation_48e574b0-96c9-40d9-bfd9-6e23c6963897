/*
 * @Date         : 2025-07-15 16:51:51
 * @Description  : 客户池埋点
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { BuryPoint, envType } from "@guanghe-pub/onion-utils";
import axios from "axios";

const env = import.meta.env.VITE_POINT as envType;

const points = {
  get_CRMFamilyClues: {
    category: "tmp",
    desc: "工号查询页曝光",
    data: [
      "familyId",
      "u_phone",
      "vistiorId",
      "unionId",
      "u_user",
      "uuid",
      "userId"
    ]
  }
};

export default new BuryPoint(points, {
  contextData: {
    productId: "38"
  },
  env,
  axios
});
