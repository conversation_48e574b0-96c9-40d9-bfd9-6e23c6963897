<!--
 * @Date         : 2024-05-11 16:00:15
 * @Description  : 积分管理设置
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts" name="PointRuleSetting">
import { ref, watch } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { cloneDeep } from "lodash-es";
import {
  isCoincidence,
  formatData,
  PointForm,
  handlerData
} from "./utils/index";
import {
  getPointRuleApi,
  updatePointRuleApi
} from "/@/api/pointsMall/pointRule";
import RuleTable from "./components/RuleTable.vue";
import timeChange from "/@/utils/handle/timeChange";
import dayjs from "dayjs";
import Upload from "/@/components/Upload/index.vue";

const loading = ref<boolean>(false);
const formRef = ref<FormInstance | null>();
const ruleTime = ref({
  activeTime: "",
  updateAt: ""
});
const form = ref<PointForm>({
  isWechat: false,
  wechatDay: undefined,
  wechatRuleList: [{ count: 1, point: undefined }],
  uploadInterval: undefined,
  intervalType: 1,
  isInvited: false,
  invitedRuleList: [{ count: undefined, point: undefined }],
  isBuyGood: false,
  buyGoodRuleList: [{ min: undefined, max: undefined, point: undefined }],
  isCumulative: false,
  cumulativeList: [{ min: undefined, max: undefined, point: undefined }],
  activeBgImage: [],
  invitedBtn: [],
  downPosterBtn: []
});
const uploadRef = ref();
const uploadInvitedBtnRef = ref();
const uploaddownPosterBtnRef = ref();

const errorMap = ref({
  buyGood: "",
  cumulative: ""
});

const rules: FormRules = {
  uploadInterval: [
    {
      required: true,
      type: "number",
      message: "请输入间隔时间",
      trigger: "blur"
    }
  ],
  activeBgImage: [
    {
      required: true,
      message: "请上传海报图",
      trigger: "blur"
    }
  ],
  invitedBtn: [
    {
      required: true,
      message: "请上传【邀请好友】按钮图",
      trigger: "blur"
    }
  ],
  downPosterBtn: [
    {
      required: true,
      message: "请上传【下载专属海报】按钮图",
      trigger: "blur"
    }
  ]
};

const validaRule = () => {
  // 判断是否有重合区间，空值为无穷大
  if (isCoincidence(form.value.buyGoodRuleList)) {
    errorMap.value.buyGood = "规则区间有重叠,请重新设置";
    ElMessage.warning("好友单笔购课金额规则区间有重叠,请重新设置");
    return true;
  }
  errorMap.value.buyGood = "";
  if (isCoincidence(form.value.cumulativeList)) {
    errorMap.value.cumulative = "规则区间有重叠,请重新设置";
    ElMessage.warning("好友累计购课金额规则区间有重叠,请重新设置");
    return true;
  }
  errorMap.value.cumulative = "";
  return false;
};

const addRow = (type: string) => {
  const info = cloneDeep(form.value[type][0]);
  for (const key in info) {
    info[key] = undefined;
  }
  form.value[type].push(info);
};

const deleteRow = (index: number, type: string) => {
  form.value[type].splice(index, 1);
};

watch(
  () => form.value.isCumulative,
  newIsCumulative => {
    if (newIsCumulative) {
      form.value.isBuyGood = true;
    }
  }
);

watch(
  () => form.value.isBuyGood,
  newIsBuyGood => {
    if (!newIsBuyGood) {
      form.value.isCumulative = false;
    }
  }
);

const checkInvited = (rule, value, callback) => {
  const len = form.value.invitedRuleList.filter(
    item => item.count === value
  ).length;
  if (len > 1) {
    callback(new Error("存在相同的邀请人数"));
  } else {
    callback();
  }
};

const checkWechat = (rule, value, callback) => {
  if (value < 2) {
    callback(new Error("次数需要大于1"));
    return;
  }
  const len = form.value.wechatRuleList.filter(
    item => item.count === value
  ).length;
  if (len > 1) {
    callback(new Error("存在相同的次数"));
  } else {
    callback();
  }
};

const setData = () => {
  formRef.value.validate(valid => {
    if (valid) {
      if (validaRule()) return;
      const data = handlerData(form.value);
      const dataStr = JSON.stringify(data);
      loading.value = true;
      updatePointRuleApi({ rule: dataStr })
        .then(res => {
          ElMessage.success("保存成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

let getData = () => {
  loading.value = true;
  getPointRuleApi()
    .then(res => {
      ruleTime.value.activeTime = timeChange(res.data.activeTime, 3);
      ruleTime.value.updateAt = timeChange(res.data.updateAt, 3);
      const data = formatData(JSON.parse(res.data.rule));
      form.value = data;

      nextTick(() => {
        uploadRef.value?.init();
        uploadInvitedBtnRef.value?.init();
        uploaddownPosterBtnRef.value?.init();
      });
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div class="g-wrapper-no">积分规则设置</div>
      <div
        class="bg-#eaf5ff w-100% p-10px"
        v-if="dayjs().isBefore(ruleTime.activeTime)"
      >
        当前规则版本于
        {{ ruleTime.updateAt }}
        修改，将于
        {{ ruleTime.activeTime }}
        日生效
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-suffix="："
        label-width="200px"
      >
        <!-- 发朋友圈 -->
        <el-form-item label="发朋友圈得积分" prop="isWechat">
          <el-switch v-model="form.isWechat" />
        </el-form-item>
        <el-form-item v-if="form.isWechat" label="发朋友圈规则详情" required>
          <div>
            <el-table
              :data="form.wechatRuleList"
              border
              highlight-current-row
              align="center"
            >
              <el-table-column prop="day" label="X天内" width="220px">
                <template #default="{ $index }">
                  <el-form-item
                    v-if="$index > 0"
                    prop="wechatDay"
                    :rules="[
                      {
                        required: true,
                        message: '请输入几天内',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="form.wechatDay"
                      :min="0"
                      :max="99999999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="count"
                label="第几次上传朋友圈"
                width="240px"
              >
                <template #default="{ row, $index }">
                  <el-form-item
                    v-if="$index > 0"
                    :prop="'wechatRuleList.' + $index + '.count'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入第几次上传朋友圈',
                        trigger: 'blur'
                      },
                      {
                        validator: checkWechat,
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="row.count"
                      :min="0"
                      :max="99999999"
                      style="width: 100%"
                    />
                  </el-form-item>
                  <template v-else>首次上传朋友圈</template>
                </template>
              </el-table-column>
              <el-table-column prop="point" label="可得积分" width="240px">
                <template #default="{ row, $index }">
                  <el-form-item
                    :prop="'wechatRuleList.' + $index + '.point'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入可获得积分',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="row.point"
                      :min="0"
                      :max="99999999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="140px">
                <template #default="{ $index }">
                  <el-button
                    v-if="$index !== 0"
                    link
                    type="primary"
                    @click="deleteRow($index, 'wechatRuleList')"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              class="mt-10px"
              type="primary"
              @click="addRow('wechatRuleList')"
            >
              新增
            </el-button>
          </div>
        </el-form-item>
        <el-form-item
          v-if="form.isWechat"
          label="上传朋友圈间隔规则"
          prop="uploadInterval"
        >
          <el-radio-group v-model="form.intervalType">
            <el-radio :label="1" class="w-full">
              下一次上传朋友圈距离上一次上传时间需超过
              <el-input-number
                v-model="form.uploadInterval"
                :min="0"
                :max="99999999"
                :step="1"
                :precision="0"
                :disabled="form.intervalType === 2"
              />
              天
            </el-radio>
            <el-radio :label="2">
              下一次上传朋友圈需与上一次上传时间不在同一个自然周
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 邀请好友 -->
        <el-form-item label="邀请好友激活得积分" prop="isInvited">
          <el-switch v-model="form.isInvited" />
        </el-form-item>
        <el-form-item
          v-if="form.isInvited"
          label="邀请好友激活规则详情"
          required
        >
          <div>
            <el-table
              :data="form.invitedRuleList"
              border
              highlight-current-row
              align="center"
            >
              <el-table-column
                prop="count"
                label="邀请并激活好友人数"
                width="220px"
              >
                <template #default="{ row, $index }">
                  <el-form-item
                    :prop="'invitedRuleList.' + $index + '.count'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入邀请并激活好友人数',
                        trigger: 'blur'
                      },
                      {
                        validator: checkInvited,
                        trigger: 'change'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="row.count"
                      :min="0"
                      :max="99999999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="point" label="可获得积分" width="240px">
                <template #default="{ row, $index }">
                  <el-form-item
                    :prop="'invitedRuleList.' + $index + '.point'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入可获得积分',
                        trigger: 'blur'
                      }
                    ]"
                  >
                    <el-input-number
                      v-model="row.point"
                      :min="0"
                      :max="99999999"
                      style="width: 100%"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="140px">
                <template #default="{ $index }">
                  <el-button
                    v-if="$index !== 0"
                    link
                    type="primary"
                    @click="deleteRow($index, 'invitedRuleList')"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              class="mt-10px"
              type="primary"
              @click="addRow('invitedRuleList')"
            >
              新增
            </el-button>
          </div>
        </el-form-item>

        <!-- 好友购课 -->
        <el-form-item label="好友购课得积分" prop="isBuyGood">
          <el-switch v-model="form.isBuyGood" />
        </el-form-item>
        <el-form-item
          label="好友购课规则详情"
          props="buyGoodRule"
          required
          :error="errorMap.buyGood"
          v-if="form.isBuyGood"
        >
          <div>
            <RuleTable
              v-model:list="form.buyGoodRuleList"
              type="buyGoodRuleList"
              title="好友单笔购课金额"
              style="width: 100%"
            />
          </div>
        </el-form-item>

        <!-- 好友累计购课 -->
        <el-form-item label="好友累计购课得积分" prop="isCumulative">
          <el-switch v-model="form.isCumulative" />
        </el-form-item>
        <el-form-item
          label="好友累计购课规则详情"
          props="cumulativeList"
          :error="errorMap.cumulative"
          v-if="form.isCumulative"
        >
          <div>
            <RuleTable
              v-model:list="form.cumulativeList"
              type="cumulativeList"
              title="好友累计购课金额"
              style="width: 100%"
            />
          </div>
        </el-form-item>
        <el-form-item label="上传海报图" prop="activeBgImage">
          <Upload
            ref="uploadRef"
            v-model:path="form.activeBgImage"
            :size="1"
            :max="1"
          />
        </el-form-item>
        <el-form-item label="上传【邀请好友】按钮图" prop="invitedBtn">
          <Upload
            ref="uploadInvitedBtnRef"
            v-model:path="form.invitedBtn"
            :size="1"
            :max="1"
          />
        </el-form-item>
        <el-form-item label="上传【下载专属海报】按钮图" prop="downPosterBtn">
          <Upload
            ref="uploaddownPosterBtnRef"
            v-model:path="form.downPosterBtn"
            :size="1"
            :max="1"
          />
        </el-form-item>
        <el-form-item label-width="100">
          <div class="w-full flex justify-center">
            <el-button type="primary" @click="setData">确定</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input-number .el-input) {
  width: 100%;
}
:deep(.el-form-item__error) {
  position: relative;
  z-index: 10;
  top: 0;
}
</style>
