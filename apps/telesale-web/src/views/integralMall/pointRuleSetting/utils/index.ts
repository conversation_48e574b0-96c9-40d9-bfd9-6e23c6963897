/*
 * @Date         : 2024-05-11 18:49:01
 * @Description  : 工具函数
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { cloneDeep } from "lodash-es";
import { PointRuleJson, Rule, Condition } from "/@/api/pointsMall/pointRule";

export interface PointForm {
  isWechat: boolean;
  wechatDay: number;
  wechatRuleList: WechatRuleList[];
  uploadInterval: number;
  intervalType: number; // 1 天 2 周
  isInvited: boolean;
  invitedRuleList: WechatRuleList[];
  isBuyGood: boolean;
  buyGoodRuleList: RuleList[];
  isCumulative: boolean;
  cumulativeList: RuleList[];
  activeBgImage: string[];
  invitedBtn: string[];
  downPosterBtn: string[];
}

interface RuleList {
  min: number;
  max?: number;
  point: number;
}

interface WechatRuleList {
  count: number;
  point: number;
}

export const isCoincidence = (list: RuleList[]) => {
  const myList = cloneDeep(list);
  let nullNumber = 0;
  // 正规化区间，处理 max 为空字符串的情况
  myList.forEach(range => {
    if (!range.max) {
      range.max = Infinity;
      nullNumber++;
    }
  });

  if (nullNumber > 1) return true;

  // 按 min 排序区间
  myList.sort((a, b) => a.min - b.min);

  // 检查重叠
  for (let i = 0; i < myList.length - 1; i++) {
    if (myList[i].max > myList[i + 1].min) {
      return true; // 发现重叠
    }
  }

  return false; // 未发现重叠
};

/**
 * @description: 格式化单表数据
 */
export const formatData = (data: PointRuleJson[]) => {
  const returns = {
    isWechat: false,
    wechatDay: undefined,
    wechatRuleList: [{ count: 1, point: undefined }],
    uploadInterval: undefined,
    intervalType: undefined,
    isInvited: false,
    invitedRuleList: [{ count: undefined, point: undefined }],
    isBuyGood: false,
    buyGoodRuleList: [{ min: undefined, max: undefined, point: undefined }],
    isCumulative: false,
    cumulativeList: [{ min: undefined, max: undefined, point: undefined }],
    activeBgImage: [],
    invitedBtn: [],
    downPosterBtn: []
  };
  console.log(data);

  data.forEach(item => {
    switch (item.type) {
      case 1:
        returns.isWechat = item.enable;
        returns.wechatDay = item.dayLimit;
        returns.uploadInterval = item.uploadInterval;
        returns.intervalType = item.intervalType;
        returns.wechatRuleList = item.rule.map(rule => {
          return {
            count: rule.conditions?.[0].value,
            point: rule.points
          };
        });
        break;

      case 3:
        returns.isBuyGood = item.enable;
        returns.buyGoodRuleList = getConditionsValue(item.rule);
        console.log("buyGoodRuleList", returns.buyGoodRuleList);

        break;

      case 6:
        returns.isCumulative = item.enable;
        returns.cumulativeList = getConditionsValue(item.rule);
        console.log("cumulativeList", returns.cumulativeList);
        break;

      case 9:
        returns.isInvited = item.enable;
        returns.invitedRuleList = item.rule.map(rule => {
          return {
            count:
              rule.conditions?.find(item => item.operator === "eq")?.value ||
              undefined,
            point: rule.points
          };
        });
        break;

      case 10:
        returns.activeBgImage = item.activeBgImage;
        break;
      case 11:
        returns.invitedBtn = item.invitedBtn;
        break;
      case 12:
        returns.downPosterBtn = item.downPosterBtn;
        break;

      default:
        break;
    }
  });
  return returns;
};

const getConditionsValue = (rule: Rule[]) => {
  const arr = [];
  rule.forEach(item => {
    const ruelItem = {
      min: undefined,
      max: undefined,
      point: item.points
    };
    item.conditions.forEach(cond => {
      if (cond.operator === "gt") {
        ruelItem.min = cond.value;
      }
      if (cond.operator === "lte") {
        ruelItem.max = cond.value;
      }
    });

    arr.push(ruelItem);
  });
  return arr;
};

/**
 * @description: 表单提交数据处理
 */
export const handlerData = (data: PointForm) => {
  const returns: PointRuleJson[] = [
    {
      type: 1,
      enable: data.isWechat,
      dayLimit: data.wechatDay,
      uploadInterval: data.uploadInterval,
      intervalType: data.intervalType,
      rule: data.wechatRuleList.map(item => {
        return {
          points: item.point,
          conditions: [
            {
              operator: "eq",
              value: item.count
            }
          ]
        };
      })
    },
    {
      type: 9,
      enable: data.isInvited,
      rule: data.invitedRuleList.map(item => {
        return {
          points: item.point,
          conditions: [
            {
              operator: "eq",
              value: item.count
            }
          ]
        };
      })
    },
    {
      type: 3,
      enable: data.isBuyGood,
      rule: getRuleList(data.buyGoodRuleList)
    },
    {
      type: 6,
      enable: data.isCumulative,
      rule: getRuleList(data.cumulativeList)
    },
    {
      type: 10,
      activeBgImage: data.activeBgImage
    },
    {
      type: 11,
      invitedBtn: data.invitedBtn
    },
    {
      type: 12,
      downPosterBtn: data.downPosterBtn
    }
  ];

  return returns;
};

const getRuleList = (ruleList: RuleList[]) => {
  const arr = ruleList.map(item => {
    const maxItem: Condition[] = !item.max
      ? []
      : [
          {
            operator: "lte",
            value: item.max || undefined
          }
        ];
    const ruelItem: Rule = {
      points: item.point,
      conditions: [
        {
          operator: "gt",
          value: item.min
        },
        ...maxItem
      ]
    };
    return ruelItem;
  });
  return arr;
};
