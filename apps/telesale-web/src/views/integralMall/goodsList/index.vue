<script setup lang="ts" name="goodsList">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import paramsHandle from "/@/utils/handle/paramsHandle";
import filterChanges from "/@/utils/handle/filterChanges";
import RePagination from "/@/components/RePagination/index.vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";

import { useDetail } from "./utils/toDetails";
import listHeader from "./utils/listHeader";
import operation from "./utils/operation";
import { getListGoods } from "/@/api/integralMall";
import { getPic } from "/@/api/order";
import {
  deleteGoodsApi,
  setGoodsStatusApi
} from "@telesale/server/src/api/pointsMall/goods";

const { toDetail } = useDetail();

let device = useAppStoreHook().device;

//带分页列表数据必备
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);
const tableRefs = ref();

//分页
const rePaginationRefs = ref();
function onSearch() {
  rePaginationRefs.value.onSearch();
}

//表头筛选
function filterChange(row) {
  filterChanges(
    row,
    [
      { name: "goodsType", val: undefined },
      { name: "upShelfState", val: undefined },
      { name: "isNeedPackage", val: 0 }
    ],
    form
  );
  onSearch();
}

//form查询
const form = reactive({
  name: "",
  goodsType: undefined,
  upShelfState: undefined,
  isNeedPackage: undefined
});

function getList() {
  loading.value = true;
  const params = paramsHandle(form, {
    minus: ["goodsType", "upShelfState"],
    pageIndex: rePaginationRefs.value.pageIndex,
    pageSize: rePaginationRefs.value.pageSize
  });

  getListGoods(params)
    .then(({ data }: { data: any }) => {
      data.list.forEach(item => {
        item.picUrl = "";
        item.skusName = "";
        item.skus.forEach(ele => {
          item.skusName += `${ele.specification}：${ele.integral} 积分<br/>`;
        });
      });
      dataList.value = data.list;
      getPicUrl();
      total.value = data.total;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      loading.value = false;
    });
}

function getPicUrl() {
  dataList.value.forEach((item, index) => {
    getPic(item.frontCover).then(({ data }: { data: any }) => {
      dataList.value[
        index
      ].picUrl = `<img width="100" height="100" src="${data}" alt="图片" />`;
    });
  });
}

function parantMath({ key, params }) {
  switch (key) {
    case "edit":
      toDetail({ type: "edit", id: params.id + "" });
      break;
    case "details":
      toDetail({ type: "detail", id: params.id + "" });
      break;
    case "del":
      updateMath(params, deleteGoodsApi);
      break;
    case "up":
      updateMath(params, setGoodsStatusApi);
      break;
    case "down":
      updateMath(params, setGoodsStatusApi);
      break;
  }
}

function add() {
  toDetail({ type: "add" });
}

const down = () => {
  const ids = tableRefs.value?.handleSelectionChange();
  if (!ids.length) {
    ElMessage.warning("您未选择任何商品");
    return;
  }
  ElMessageBox.confirm("是否批量下架选中的商品？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;
    setGoodsStatusApi({ id: ids.map(item => item.id), status: 1 })
      .then(() => {
        ElMessage.success("操作成功");
        getList();
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const del = () => {
  const ids = tableRefs.value?.handleSelectionChange();
  if (!ids.length) {
    ElMessage.warning("您未选择任何商品");
    return;
  }
  ElMessageBox.confirm("是否批量删除选中的商品？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;

    deleteGoodsApi({ id: ids.map(item => item.id) })
      .then(() => {
        ElMessage.success("操作成功");
        getList();
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

function updateMath(row, math) {
  loading.value = true;
  math({ id: [row.id], status: row.upShelfState === 1 ? 2 : 1 })
    .then(() => {
      ElMessage.success("操作成功");
      getList();
    })
    .catch(() => {
      loading.value = false;
    });
}

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="clearfix"
        @submit.prevent
      >
        <el-form-item prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入商品名称"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item class="g-set-button">
          <el-button
            type="primary"
            plain
            @click="down"
            v-if="
              useUserStoreHook().authorizationMap.indexOf(
                'telesale_admin_mall_goods_set'
              ) > -1
            "
          >
            批量下架
          </el-button>
          <el-button
            type="danger"
            @click="del"
            v-if="
              useUserStoreHook().authorizationMap.indexOf(
                'telesale_admin_mall_goods_set'
              ) > -1
            "
          >
            批量删除
          </el-button>
          <el-button
            type="primary"
            @click="add"
            v-if="
              useUserStoreHook().authorizationMap.indexOf(
                'telesale_admin_mall_goods_set'
              ) > -1
            "
          >
            新增
          </el-button>
        </el-form-item>
      </el-form>
      <div class="g-table-box">
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
          :widthOperation="210"
          :filterChange="filterChange"
          :selection="
            useUserStoreHook().authorizationMap.indexOf(
              'telesale_admin_mall_goods_set'
            ) > -1
          "
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          @parantMath="parantMath"
        />
      </div>
      <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
    </el-card>
  </div>
</template>
