<!--
 * @Date         : 2025-03-28 16:58:14
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts" name="GroupDataAnalysis">
import MonthlyRevenue from "./components/MonthlyRevenue.vue";
import MonthlyRevenueCalendar from "./components/MonthlyRevenueCalendar.vue";
import { useSelectGroup } from "/@/hooks/business/dataAnalysis";

const { groupList, workerList, orgId } = useSelectGroup();
</script>

<template>
  <div class="g-margin-20">
    <el-row :gutter="10" v-if="orgId">
      <el-col :span="12">
        <MonthlyRevenue :orgId="orgId" />
      </el-col>
      <el-col :span="12">
        <MonthlyRevenueCalendar :orgId="orgId" :workerList="workerList" />
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.el-card {
  margin-top: 10px;
}
</style>
