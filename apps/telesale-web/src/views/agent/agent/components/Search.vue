<script setup lang="ts">
import { reactive, ref } from "vue";
import { FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "/@/store/modules/user";
import { uploadAgent } from "/@/api/agent";
import UploadDataEcho from "../dialog/UploadDataEcho.vue";
import UploadFileSelect from "../dialog/UploadFileSelect.vue";
import AgentSet from "../dialog/AgentSet.vue";
import stageList from "/@/utils/data/stageList";
import findOrganizationMath from "/@/utils/asyn/findOrganization";
import typeChange from "/@/utils/handle/typeChange";
import dateChange from "../utils/dateChange";

interface Emits {
  (e: "onSearch", val: boolean): void;
  (e: "resetFitler"): void;
  (e: "update:loading", val: boolean): void;
}

const emit = defineEmits<Emits>();

function onSearch(val = false) {
  emit("onSearch", val);
}

let isUploadLimit =
  useUserStoreHook().authorizationMap.indexOf("telesale_admin_agent_upload") >
  -1;

const isModelMail = ref(false);
const isModelUpload = ref(false);
const isModelAdd = ref(false);
const dataMemory = ref();
const isUpload = ref(false);
//form查询
const form = reactive({
  mail: "",
  name: undefined,
  phone: "",
  qmExten: "",
  stage: "",
  wechat: "",
  channelId: "",
  employmentNumber: undefined,
  status: undefined,
  base: undefined
});

const formRef = ref<FormInstance>();

const inputEmploymentNumber = str => {
  if (str.length > 0) {
    str =
      str.length > 7 ? str.substr(1).padStart(8, "0") : str.padStart(8, "0");
    form.employmentNumber = str;
  }
};

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  form.channelId = "";
  form.status = undefined;
  form.base = undefined;
  emit("resetFitler");
  form;

  onSearch(true);
};

function uploadFile(file) {
  let formData = new FormData();
  formData.append("file", file.file);
  emit("update:loading", true);
  uploadAgent(formData)
    .then(({ data }: { data: any }) => {
      let isCanUpload = false;
      data.forEach(item => {
        item.group = item.organization.name;
        item.mail = item.worker.mail;
        item.stage = item.worker.stage;
        item.joinAt = dateChange(item.worker, "joinAt");
        item.agentNO = item.repeater ? item.repeater.agentNO : "";
        item.baseCN = typeChange(item.worker.base, useUserStoreHook().baseList);
        !isCanUpload && (isCanUpload = !!item.msg);
      });
      dataMemory.value = data;
      isUpload.value = isCanUpload;
      emit("update:loading", false);
      isModelUpload.value = true;
    })
    .catch(() => {
      emit("update:loading", false);
    });
}

const orgData = ref<any[]>([]);
async function add() {
  emit("update:loading", true);
  orgData.value = await findOrganizationMath();
  emit("update:loading", false);
  isModelAdd.value = true;
}

defineExpose({
  form
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
    <el-form-item prop="name">
      <el-select-v2
        v-model="form.name"
        filterable
        clearable
        value-key="name"
        :options="useUserStoreHook().allAgentList"
        placeholder="请选择坐席"
      />
    </el-form-item>
    <el-form-item prop="mail">
      <el-input
        v-model.trim="form.mail"
        placeholder="请输入坐席邮箱"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="phone">
      <el-input
        v-model.trim="form.phone"
        placeholder="请输入坐席手机号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="wechat">
      <el-input
        v-model.trim="form.wechat"
        placeholder="请输入坐席微信号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="qmExten">
      <el-input
        v-model.trim="form.qmExten"
        placeholder="请输入外呼工号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="stage">
      <el-select v-model="form.stage" placeholder="请选择学段" clearable>
        <el-option
          v-for="item in stageList"
          :key="item.value"
          :label="item.value"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="employmentNumber">
      <el-input
        v-model.trim="form.employmentNumber"
        placeholder="00000000"
        clearable
        @input="inputEmploymentNumber"
        @keyup.enter="onSearch"
      >
        <template #prepend> YC </template>
      </el-input>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">
        重置
      </el-button>
    </el-form-item>
    <el-form-item class="g-set-button">
      <el-button
        type="primary"
        v-if="isUploadLimit"
        @click="isModelMail = true"
        style="margin-right: 10px"
      >
        批量修改邮箱
      </el-button>
      <el-upload
        action="#"
        :http-request="uploadFile"
        :auto-upload="true"
        :show-file-list="false"
        accept=".xlsx"
        v-if="isUploadLimit"
      >
        <el-button type="primary">上传 </el-button>
      </el-upload>
      <el-button
        style="margin-left: 10px"
        type="primary"
        @click="add"
        v-if="
          useUserStoreHook().authorizationMap.indexOf(
            'telesale_admin_worker_edit'
          ) > -1
        "
      >
        新增坐席
      </el-button>
    </el-form-item>
    <UploadDataEcho
      :list="dataMemory"
      v-model:value="isModelUpload"
      :isUpload="isUpload"
      @onSearch="onSearch"
    />
    <AgentSet
      v-model:value="isModelAdd"
      :dataMemory="{}"
      @getList="onSearch"
      v-if="isModelAdd"
      dialogType="add"
      :orgData="orgData"
    />
    <UploadFileSelect
      v-if="isModelMail"
      v-model:value="isModelMail"
      @onSearch="onSearch"
    />
  </el-form>
</template>
