<script lang="ts" setup>
import { computed, ref, onUnmounted } from "vue";
import { exportAgent, taskNew } from "/@/api/agent";
import ReTable from "/@/components/ReTable/index.vue";

interface Props {
  value: boolean;
  isUpload: boolean;
  list: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    props.value && (tableData.value = props.list);
    return props.value;
  },
  set(val: boolean) {
    isImport.value = false;
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
  emit("onSearch");
}

const loading = ref<Boolean>(false);
const isImport = ref<Boolean>(false);
const tableData = ref([]);
const listHeader = ref([
  { field: "group", desc: "所属小组", minWidth: 110 },
  { field: "mail", desc: "邮箱", minWidth: 150 },
  { field: "stage", desc: "学段", minWidth: 100 },
  { field: "joinAt", desc: "入职时间", minWidth: 120 },
  { field: "callConfiguration", desc: "外呼渠道", minWidth: 130 },
  { field: "agentNO", desc: "外呼工号", minWidth: 90 },
  { field: "baseCN", desc: "base地", minWidth: 90 },
  { field: "msg", desc: "异常说明", minWidth: 150 }
]);

function find(id) {
  taskNew({ id })
    .then(({ data }: { data: any }) => {
      if (data?.status === 2) {
        clearTimeout(time.value);
        tableData.value = data.list.map((item, index) => {
          return {
            ...props.list[index],
            msg: item.msg || "录入成功"
          };
        });
        loading.value = false;
      } else {
        timeOut(id);
      }
    })
    .catch(() => {
      loading.value = false;
      clearTimeout(time.value);
      isImport.value = false;
    });
}
function query() {
  loading.value = true;
  isImport.value = true;
  exportAgent({ data: props.list })
    .then(({ data }: { data: any }) => {
      timeOut(data.id);
    })
    .catch(() => {
      loading.value = false;
      isImport.value = false;
    });
}

const time = ref();
function timeOut(id) {
  time.value = setTimeout(() => {
    find(id);
  }, 5000);
}
onUnmounted(() => {
  clearTimeout(time.value);
});
</script>

<template>
  <el-dialog
    title="导入数据展示"
    v-model="isModel"
    :destroy-on-close="true"
    :before-close="handleClose"
    fullscreen
  >
    <div v-loading="loading">
      <div class="d-head">
        <el-popconfirm title="确定导入这些数据吗？" @confirm="query">
          <template #reference>
            <el-button type="primary" :disabled="isImport || isUpload">
              数据导入
            </el-button>
          </template>
        </el-popconfirm>
      </div>
      <ReTable :dataList="tableData" :listHeader="listHeader" />
    </div>
  </el-dialog>
</template>

<style scoped>
.d-head {
  position: fixed;
  left: 48%;
  top: 11px;
}
</style>
