# 优秀录音组件说明

## 📖 组件概述

本目录包含优秀录音页面的子组件，实现了Tab拆分功能，将原优秀录音页面拆分为两个独立的组件。

## 📁 组件结构

```
components/
├── FirstExcellentRecording.vue    # 首通优秀录音库组件
├── FollowUpRecording.vue          # 后续跟进录音库组件
└── README.md                      # 本文件
```

## 🔧 组件详情

### FirstExcellentRecording.vue - 首通优秀录音库

**功能描述：**
- 展示AI评级为A级且人工审核通过的优质录音
- 自动同步AI案例库中符合条件的录音数据

**主要特性：**
- ✅ 坐席名称搜索（支持混合查询）
- ✅ 通话ID搜索
- ✅ 通话时间范围搜索
- ✅ 通话时长范围搜索（最小/最大时长）
- ✅ 默认显示当月数据
- ✅ 通话ID一键复制功能
- ✅ 评分总结智能提取和悬浮查看
- ✅ 查看通话详情功能

**API接口：**
- 使用 `getExcellentCasesList` API
- 筛选条件：`aiAppraiseScore: "A"`, `humanAppraiseResult: "通过"`, `showPage: 1`

**事件：**
- `@view-call`: 查看通话详情事件

### FollowUpRecording.vue - 后续跟进录音库

**功能描述：**
- 展示AI评级为A级但人工审核不通过，手动添加的录音
- 支持录音标签分类管理

**主要特性：**
- ✅ 坐席名称搜索（支持混合查询）
- ✅ 通话ID搜索
- ✅ 通话时间范围搜索
- ✅ 通话时长范围搜索（最小/最大时长）
- ✅ 录音标签表头筛选功能（使用nexus-table-filter-column）
- ✅ 默认显示当月数据
- ✅ 通话ID一键复制功能
- ✅ 评分总结智能提取和悬浮查看
- ✅ 查看通话详情功能

**录音标签枚举：**
- 问题处理
- 优惠包装
- 截杀关单
- 其他

**数据源：**
- 使用 `getExcellentCasesList` API
- 筛选条件：`aiAppraiseScore: "A"`, `showPage: 2`
- 支持 `audioTags` 参数进行录音标签筛选
- 从API返回数据中提取录音标签信息

**事件：**
- `@view-call`: 查看通话详情事件

## 🔄 组件通信

### 父子组件通信

```typescript
// 父组件 (index.vue)
<FirstExcellentRecording @view-call="viewCall" />
<FollowUpRecording @view-call="viewCall" />

// 子组件事件定义
const emit = defineEmits<{
  viewCall: [row: any];
}>();

// 子组件事件触发
emit("viewCall", rowData);
```

### 暴露的方法

两个子组件都暴露了以下方法供父组件调用：

```typescript
defineExpose({
  onSearch,    // 执行搜索
  resetForm    // 重置表单
});
```

## 🎯 技术实现

### 共同特性

1. **组件复用**
   - AgentSelect: 坐席选择组件
   - NexusTable: 数据表格组件
   - NexusForm: 搜索表单组件
   - NexusDatePicker: 时间选择组件

2. **状态管理**
   - 独立的搜索表单状态
   - 响应式数据绑定
   - 表单验证和重置

3. **用户体验**
   - 默认当月时间范围
   - 智能搜索参数构建
   - 评分总结内容提取
   - 悬浮提示功能

### 差异化实现

#### FirstExcellentRecording
- 使用真实API接口
- 标准的API参数映射
- 生产环境数据展示

#### FollowUpRecording
- 使用真实API接口
- 新增录音标签筛选列
- 使用nexus-table-filter-column实现表头筛选
- 支持showPage=2筛选后续跟进录音库数据
- 支持audioTags参数进行标签筛选

## 📊 数据结构

### 搜索表单数据结构

```typescript
const searchForm = reactive({
  workerId: undefined,      // 坐席ID
  callId: "",              // 通话ID
  minDuration: undefined,   // 最小通话时长
  maxDuration: undefined,   // 最大通话时长
  timeRange: null          // 时间范围
});
```

### 录音数据结构

```typescript
interface RecordingItem {
  id: string;                    // 记录ID
  actionId: string;              // 通话ID
  recordingTag?: string;         // 录音标签（仅后续跟进录音）
  createdAt: number;             // 创建时间
  workerName: string;            // 坐席名称
  lastGroupName: string;         // 所属小组
  callTimeLength: number;        // 通话时长
  aiAppraise: {                  // AI评级信息
    score: string;               // 评级分数
    result: string;              // 评级结果
  };
}
```

## 🔮 后续扩展

### 待实现功能

1. **后端API支持**
   - 后续跟进录音的真实API接口
   - 录音标签字段的存储和查询
   - 手动添加录音的接口

2. **功能增强**
   - 录音标签的动态配置
   - 更多筛选条件支持
   - 批量操作功能

3. **性能优化**
   - 虚拟滚动支持
   - 数据缓存机制
   - 懒加载实现

## 📝 使用示例

```vue
<template>
  <div>
    <!-- 使用首通优秀录音组件 -->
    <FirstExcellentRecording 
      @view-call="handleViewCall" 
      ref="firstRecordingRef"
    />
    
    <!-- 使用后续跟进录音组件 -->
    <FollowUpRecording 
      @view-call="handleViewCall"
      ref="followRecordingRef" 
    />
  </div>
</template>

<script setup>
import FirstExcellentRecording from './components/FirstExcellentRecording.vue';
import FollowUpRecording from './components/FollowUpRecording.vue';

const handleViewCall = (row) => {
  // 处理查看通话详情
  console.log('查看通话:', row);
};

// 调用子组件方法
const refreshData = () => {
  firstRecordingRef.value?.onSearch();
  followRecordingRef.value?.onSearch();
};
</script>
```

## 🔗 相关文档

- [优秀录音页面README](../README.md)
- [需求文档](../../../Docs/Demand/后续跟进录音/)
- [API接口文档](../../../api/AIQualityInspection/excellentCases.ts)
