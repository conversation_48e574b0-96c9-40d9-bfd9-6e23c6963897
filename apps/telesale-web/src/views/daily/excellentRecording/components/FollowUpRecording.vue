<!--
 * @Date         : 2025-01-09
 * @Description  : 后续跟进录音库
 * @Autor        : AI Assistant
 * @LastEditors  : Qzr(<EMAIL>)
 -->

<template>
  <div>
    <!-- 搜索表单 -->
    <nexus-form ref="searchFormRef" v-model="searchForm" inline>
      <el-form-item prop="workerId">
        <AgentSelect v-model="searchForm.workerId" @change="onAgentChange" />
      </el-form-item>

      <el-form-item prop="callId">
        <el-input
          v-model.trim="searchForm.callId"
          placeholder="请输入通话ID"
          clearable
          @keyup.enter="onSearch"
        />
      </el-form-item>

      <el-form-item prop="duration">
        <el-input-number
          v-model="searchForm.minDuration"
          :min="0"
          :step="1"
          step-strictly
          placeholder="最小通话时长/s"
          :controls="false"
        />
        至
        <el-input-number
          v-model="searchForm.maxDuration"
          :min="0"
          :step="1"
          step-strictly
          placeholder="最大通话时长/s"
          :controls="false"
        />
      </el-form-item>

      <el-form-item prop="timeRange">
        <nexus-date-picker
          ref="timeRangePickerRef"
          v-model="searchForm.timeRange"
          type="daterange"
          :show-shortcuts="true"
          placeholder="请选择时间范围"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </nexus-form>

    <!-- 数据表格 -->
    <nexus-table
      ref="TableRef"
      un-mounted
      :resFormat="data => data?.data"
      :get-list="getFollowUpRecordingList"
      data-key="list"
    >
      <el-table-column label="通话ID" prop="actionId">
        <template #default="{ row }">
          <div class="flex items-center">
            <span>{{ row.actionId }}</span>
            <el-icon
              class="ml-5px cursor-pointer text-primary"
              title="复制通话ID"
              @click="copyCallId(row.actionId)"
            >
              <DocumentCopy />
            </el-icon>
          </div>
        </template>
      </el-table-column>

      <nexus-table-filter-column
        label="录音标签"
        prop="recordingTag"
        paramKey="recordingTag"
        :tableRef="TableRef"
        :optionList="recordingTagOptions"
      >
        <template #default="{ row }">
          <el-tag v-if="row.recordingTag">
            {{ row.recordingTag }}
          </el-tag>
          <span v-else class="text-gray-400">-</span>
        </template>
      </nexus-table-filter-column>

      <el-table-column label="创建时间" prop="createdAt">
        <template #default="{ row }">
          {{ formatDateTime(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="坐席名称" prop="workerName" />

      <el-table-column label="所属小组" prop="lastGroupName" />

      <el-table-column label="通话时长" prop="callTimeLength">
        <template #default="{ row }">
          {{ durationChange(row.callTimeLength) }}
        </template>
      </el-table-column>

      <el-table-column label="评分总结" prop="aiAppraise.result">
        <template #default="{ row }">
          <div
            v-if="extractSummaryContent(row.aiAppraise?.result)"
            class="summary-content"
          >
            <el-tooltip
              effect="dark"
              placement="top"
              :content="extractSummaryContent(row.aiAppraise?.result)"
              raw-content
              popper-class="summary-tooltip"
              :disabled="!extractSummaryContent(row.aiAppraise?.result)"
            >
              <div
                class="summary-text"
                v-html="extractSummaryContent(row.aiAppraise?.result)"
              />
            </el-tooltip>
          </div>
          <span v-else class="text-gray-400">-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="viewCall(row)">
            查看通话
          </el-button>
        </template>
      </el-table-column>
    </nexus-table>
  </div>
</template>

<script setup lang="ts" name="FollowUpRecording">
import { ref, reactive, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { DocumentCopy } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import durationChange from "/@/utils/handle/durationChange";
import NexusTable from "/@/components/Nexus/NexusTable/index.vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import NexusDatePicker from "/@/components/Nexus/NexusDatePicker/index.vue";
import NexusTableFilterColumn from "/@/components/Nexus/NexusTableFilterColumn/index.vue";
import AgentSelect from "/@/components/AgentSelect/index.vue";
import {
  getExcellentCasesList,
  type ExcellentCasesQueryParams
} from "/@/api/AIQualityInspection/excellentCases";

// 定义事件
const emit = defineEmits<{
  viewCall: [row: any];
}>();

// 页面状态
const TableRef = ref();
const searchFormRef = ref();
const timeRangePickerRef = ref();

// 搜索表单数据
const searchForm = reactive({
  workerId: undefined, // 坐席ID
  callId: "", // 通话ID
  minDuration: undefined, // 最小通话时长
  maxDuration: undefined, // 最大通话时长
  timeRange: null // 时间范围
});

// 录音标签选项
const recordingTagOptions = [
  { text: "问题处理", val: "问题处理" },
  { text: "优惠包装", val: "优惠包装" },
  { text: "截杀关单", val: "截杀关单" },
  { text: "其他", val: "其他" }
];

// 获取后续跟进录音列表的API调用函数
const getFollowUpRecordingList = async (params: any) => {
  // 构建API查询参数
  const apiParams: ExcellentCasesQueryParams = {
    // 筛选条件：展示页面为后续跟进录音库
    showPage: 2 // 2表示后续跟进录音库
    // AI评级为A级（后续跟进录音的基础条件）
    // aiAppraiseScore: "A"
  };

  // 映射用户搜索参数
  if (params.callId) {
    apiParams.actionId = params.callId;
  }

  if (params.workerId) {
    apiParams.workerId = params.workerId;
  }

  if (params.startTime && params.endTime) {
    // 时间戳已经是毫秒级，getExcellentCasesList函数会自动转换为秒级
    apiParams.beginTime = params.startTime;
    apiParams.endTime = params.endTime;
  }

  // 通话时长参数
  if (params.minDuration !== undefined && params.minDuration !== null) {
    apiParams.minDuration = params.minDuration;
  }

  if (params.maxDuration !== undefined && params.maxDuration !== null) {
    apiParams.maxDuration = params.maxDuration;
  }

  // 录音标签筛选
  if (params.recordingTag) {
    apiParams.audioTags = [params.recordingTag];
  }

  // 分页参数
  if (params.pages) {
    apiParams.pages = params.pages;
  }

  if (params.pageSize) {
    apiParams.pageSize = params.pageSize;
  }

  try {
    console.log("后续跟进录音库API请求参数:", apiParams);
    const response = await getExcellentCasesList(apiParams);
    console.log("后续跟进录音库API响应:", response);

    // 处理返回数据，确保包含录音标签信息
    if (response.data?.list) {
      response.data.list = response.data.list.map((item: any) => ({
        ...item,
        // 从audioTags字段提取录音标签，audioTags为数组，取第一个成员
        recordingTag: item.audioTags?.[0] || null
      }));
    }

    console.log("后续跟进录音库处理后的数据:", response.data?.list);

    return response;
  } catch (error) {
    console.error("获取后续跟进录音列表失败:", error);
    ElMessage.error("获取录音列表失败");
    return {
      data: {
        list: [],
        total: "0"
      }
    };
  }
};

// 格式化日期时间
const formatDateTime = (timestamp: number | string) => {
  if (!timestamp) return "-";
  return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
};

// 评分总结内容提取函数
const extractSummaryContent = (content: string): string => {
  if (!content) return "";

  // 查找"评估总结"关键词
  const summaryKeyword = "评估总结";
  const summaryIndex = content.indexOf(summaryKeyword);

  if (summaryIndex === -1) {
    return "";
  }

  // 提取"评估总结"之后的内容
  let summaryContent = content.substring(summaryIndex + summaryKeyword.length);

  // 去除开头的冒号和空白字符
  if (summaryContent) {
    summaryContent = summaryContent
      .replace(/^[：:]+/g, "")
      .replace(/^(<br\s*\/?>|\s)+/i, "");
  }

  return summaryContent;
};

// 复制通话ID
const copyCallId = (callId: string) => {
  navigator.clipboard.writeText(callId).then(
    () => {
      ElMessage.success("通话ID已复制到剪切板");
    },
    () => {
      ElMessage.error("复制失败");
    }
  );
};

// 坐席变更
const onAgentChange = (value: any) => {
  searchForm.workerId = value;
};

// 查看通话详情
const viewCall = (row: any) => {
  // 提取当前行的评分总结内容
  const summaryContent = extractSummaryContent(row.aiAppraise?.result);

  // 将行数据和提取的评分总结一起传递给父组件
  const rowData = {
    ...row,
    extractedSummary: summaryContent // 添加提取的评分总结内容
  };
  emit("viewCall", rowData);
};

// 搜索功能
const onSearch = () => {
  // 构建完整的参数对象，包含所有可能的字段
  const params: any = {
    workerId: undefined,
    callId: undefined,
    minDuration: undefined,
    maxDuration: undefined,
    startTime: undefined,
    endTime: undefined,
    recordingTag: undefined
  };

  // 坐席ID参数
  if (searchForm.workerId) {
    params.workerId = searchForm.workerId;
  }

  if (searchForm.callId) {
    params.callId = searchForm.callId;
  }

  // 通话时长参数
  if (searchForm.minDuration !== undefined && searchForm.minDuration !== null) {
    params.minDuration = searchForm.minDuration;
  }

  if (searchForm.maxDuration !== undefined && searchForm.maxDuration !== null) {
    params.maxDuration = searchForm.maxDuration;
  }

  // 时间范围参数处理
  if (timeRangePickerRef.value?.unixsDatePicker) {
    const [startTime, endTime] = timeRangePickerRef.value.unixsDatePicker;
    if (startTime && endTime) {
      params.startTime = startTime;
      params.endTime = endTime;
    }
  }

  TableRef.value?.search(params);
};

// 重置表单
const resetForm = () => {
  // 重置表单字段
  searchForm.workerId = undefined;
  searchForm.callId = "";
  searchForm.minDuration = undefined;
  searchForm.maxDuration = undefined;

  // 重置为当月时间
  const now = dayjs();
  const startOfMonth = now.startOf("month");
  const endOfMonth = now.endOf("month");
  searchForm.timeRange = [startOfMonth, endOfMonth];

  // 使用 nextTick 确保时间选择器的值已经更新
  nextTick(() => {
    // 执行搜索
    onSearch();
  });
};

// 初始化
onMounted(() => {
  // 设置默认时间范围为当月
  const now = dayjs();
  const startOfMonth = now.startOf("month");
  const endOfMonth = now.endOf("month");
  searchForm.timeRange = [startOfMonth, endOfMonth];

  nextTick(() => {
    // 初始化时执行搜索
    onSearch();
  });
});

// 暴露方法给父组件
defineExpose({
  onSearch,
  resetForm
});
</script>

<style scoped>
.summary-content {
  width: 100%;
}

.summary-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  line-height: 1.4;
  max-width: 100%;
}

.summary-text:hover {
  color: #409eff;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-primary {
  color: #409eff;
}

.text-primary:hover {
  color: #66b1ff;
}

.cursor-pointer {
  cursor: pointer;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.ml-5px {
  margin-left: 5px;
}

/* 限制评分总结tooltip的宽度 */
:deep(.summary-tooltip) {
  max-width: 400px !important;
}
</style>
