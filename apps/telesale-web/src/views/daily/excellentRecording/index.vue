<!--
 * @Date         : 2025-04-23 18:34:55
 * @Description  : 优秀录音
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20">
    <el-card>
      <!-- Tab切换 -->
      <el-tabs v-model="activeTab" @tab-change="onTabChange">
        <el-tab-pane label="首通优秀录音库" name="first">
          <FirstExcellentRecording @view-call="viewCall" />
        </el-tab-pane>

        <el-tab-pane label="后续跟进录音库" name="follow">
          <FollowUpRecording @view-call="viewCall" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 查看通话抽屉 -->
    <DetailDrawer v-model:visible="drawerVisible" :detail-info="currentRow" />
  </div>
</template>

<script setup lang="ts" name="excellentRecording">
// 优秀录音页面
import { ref } from "vue";
import FirstExcellentRecording from "./components/FirstExcellentRecording.vue";
import FollowUpRecording from "./components/FollowUpRecording.vue";
import DetailDrawer from "/@/views/aiQualityInspection/excellentCases/DetailDrawer/index.vue";

// 页面状态
const activeTab = ref("first"); // 当前激活的tab
const drawerVisible = ref(false);
const currentRow = ref();

// Tab切换处理
const onTabChange = (tabName: string) => {
  activeTab.value = tabName;
};

// 查看通话详情
const viewCall = (row: any) => {
  currentRow.value = row;
  drawerVisible.value = true;
};
</script>

<style scoped>
.g-margin-20 {
  margin: 20px;
}
</style>

<style lang="scss">
/* 全局样式 - 限制评分总结tooltip的宽度 */
.summary-tooltip {
  max-width: 400px !important;
  word-wrap: break-word !important;
  white-space: normal !important;
}

/* 更强的选择器 */
.el-popper.summary-tooltip {
  max-width: 400px !important;
}

/* 针对Element Plus的tooltip内容 */
.summary-tooltip .el-tooltip__content {
  max-width: 400px !important;
  word-wrap: break-word !important;
  white-space: normal !important;
}
</style>
