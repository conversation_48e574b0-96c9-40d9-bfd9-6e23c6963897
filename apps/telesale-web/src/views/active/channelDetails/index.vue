<!--
 * @Date         : 2024-03-06 15:16:34
 * @Description  : 渠道活码编辑页
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts" name="channelDetails">
import { ref, reactive, onMounted, nextTick, computed, watch } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { closePageBox } from "/@/utils/handle/closePage";
import {
  addChannel,
  editChannel,
  getAllRuleConfig,
  getTag
} from "/@/api/active";
import rulesProps from "./utils/rules";
import { timeLongList } from "./utils/list";
import getListAll from "./utils/getListAll";
import getDetails from "./utils/getDetails";
import tagShow from "./utils/tagShow";
import TagConfigs from "./components/TagConfigs.vue";
import UIConfig from "./components/UIConfig.vue";
import WelcomeMaterial from "./components/WelcomeMaterial.vue";
import AutoSendCourse from "./components/AutoSendCourse.vue";
import RuleReferConfig from "./components/RuleReferConfig.vue";
import { getGroupListApi } from "/@/api/active/channelType";
import { getWelcomeListApi } from "/@/api/active/welcomeMessage";
import { WelcomeMessageRes } from "/@/types/active/welcomeMessage";
import dayjs from "dayjs";
import { WeChatInfo, getWeChatListApi } from "/@/api/daily/weChatWork";
import { cloneDeep } from "lodash-es";

const { closePage } = closePageBox();
let device = useAppStoreHook().device;
const loading = ref<boolean>(false);
const route = useRoute();
const id = (route.query?.id as string) || "";
const type = (route.query?.type as string) || "";
const wechatList = ref<WeChatInfo[]>([]);

const capitalizationList = ["一", "二", "三", "四", "五", "六", "日"];

function getInitData() {
  const form = {
    ruleTemplateId: "",
    fetchCustomerType: 1,
    accountId: undefined,
    isAuthCourses: false,
    courses: [],
    authDay: "",
    channelGroup: "",
    materials: [],
    config: null,
    repeatedlyTopicConfig: null,
    name: "",
    tag: [],
    remark: "",
    clueInvalidDay: 30,
    welcome: "",
    miniProgramId: "",
    welcomeMsgSwitch: 1,
    welcomeMsgId: ""
  };
  return cloneDeep(form);
}
//form查询
const form: any = reactive(getInitData());
const formRef = ref<FormInstance>();
const rules = reactive<FormRules>(rulesProps);
const miniList = ref([]);
const typeList = ref([]);
const ruleTemplateList = ref<any[]>([]);

const getTypeList = async () => {
  const res = await getGroupListApi();
  typeList.value = res.data.list;
};

const getRuelList = async () => {
  const { data } = await getAllRuleConfig();
  ruleTemplateList.value = (data as any).list;
};

const welcomeMsgList = ref<WelcomeMessageRes[]>([]);
const welcomeMaterialRefs = ref<InstanceType<typeof WelcomeMaterial>[]>();

const getWelcomeList = async () => {
  const res = await getWelcomeListApi();
  res.data.list.forEach(item => {
    item.welcomeData.forEach(data => {
      data.materials = data.materials?.map(item => {
        let obj: any =
          item[item.materialType === "MINIPROGRAM" ? "miniProgram" : "image"];
        obj.materialType = item.materialType;
        return obj;
      });
    });
  });
  welcomeMsgList.value = res.data.list;
};

const getTagList = async () => {
  if (!form.accountId) return;
  const { data }: { data: any } = await getTag({ accountId: form.accountId });
  tagListInit.value = data?.tag_group || [];
};

const currentWelcomMsg = computed(() =>
  welcomeMsgList.value?.find(item => item.id === form.welcomeMsgId)
);

const changeWelcomeMsg = () => {
  nextTick(() => {
    if (!welcomeMaterialRefs.value) return;
    currentWelcomMsg.value?.welcomeData?.forEach((item, index) => {
      welcomeMaterialRefs.value?.[index]?.getPicUrl(item.materials);
    });
  });
};

const changeWelcomeMsgSwitch = (e: number) => {
  if (e === 1) {
    form.welcomeMsgId = undefined;
  }
};

const getWechat = async () => {
  const res = await getWeChatListApi();
  wechatList.value = res.data.list;
  wechatList.value.forEach(item => {
    localStorage.removeItem(`channelTag${item.id}`);
  });
};

const changeWechat = (e: number) => {
  ruleReferConfiglRef.value.ruleChange();
  if (form.ruleTemplateId) {
    form.ruleTemplateId = undefined;
  }
};

watch(
  () => form.accountId,
  (n, o) => {
    ruleReferConfiglRef.value.ruleTemplateList = ruleTemplateList.value.filter(
      item => item.accountId === n
    );
    if (o) {
      localStorage.setItem(`channelTag${o}`, JSON.stringify(form.tag));
    }
    tagListInit.value = [];
    form.tag = JSON.parse(localStorage.getItem(`channelTag${n}`)) || [];
    getTagList();
  }
);

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      let params: any = { ...form };
      params.welcomeMsgId = params.welcomeMsgId || 0;
      // 处理首次活码主题配置
      params.config && (params.configurationId = params.config.id);
      // 处理多次活码主题配置
      params.repeatedlyTopicConfig &&
        (params.repeatedlyTopicId = params.repeatedlyTopicConfig.id);
      type === "edit" && (params.id = Number(id));
      let method = type === "add" ? addChannel : editChannel;
      params.tag = form.tag.map(item => {
        return item.id;
      });
      !params.authDay && (params.authDay = 0);
      params.materials = params.materials.map(item => {
        let obj: any = {
          materialType: item.materialType
        };
        if (item.materialType === "MINIPROGRAM") {
          obj.miniProgram = {
            title: item.title,
            appId: item.appId,
            page: item.page,
            imageURL: item.imageURL
          };
        } else {
          obj.image = {
            imageURL: item.imageURL
          };
        }
        return obj;
      });

      // 所有的线索模型，新增编辑写死30天。
      params.clueInvalidDay = 30;
      method(params)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          closePage(id, "channel");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};

const UIConfigRef = ref();
const welcomeMaterialRef = ref();
const ruleReferConfiglRef = ref();
const tagListInit = ref<any[]>([]);
let miniDefault = "";

onMounted(async () => {
  loading.value = true;
  try {
    getWechat();
    await getWelcomeList();
    await getTypeList();
    await getRuelList();
    const allList = await getListAll();
    const miniData: any[] = allList[0]?.data.list || [];
    welcomeMaterialRef.value.appletList = miniData;
    miniList.value = miniData;
    miniDefault = miniData?.find(item => item.isUsed)?.id || "";
    nextTick(() => {
      form.miniProgramId = miniDefault;
    });
    UIConfigRef.value.configList = allList[1]?.data?.list || [];

    if (type !== "add") {
      let resData = await getDetails(Number(id));
      !resData.authDay && (resData.authDay = "");
      resData.fetchCustomerType = resData.fetchCustomerType || 1;

      // 处理活码主题配置的兼容性
      if (resData.configuration?.id) {
        let obj = UIConfigRef.value.configList.find(
          item => item.id === resData.configuration.id
        );
        obj && (resData.config = obj);
      }

      // 处理活码主题配置的兼容性
      if (resData.repeatedlyTopicInfo?.id) {
        let obj = UIConfigRef.value.configList.find(
          item => item.id === resData.repeatedlyTopicInfo.id
        );
        obj && (resData.repeatedlyTopicConfig = obj);
      }

      resData.ruleTemplateId = resData.ruleTemplateId || "";

      resData.materials = resData.materials.map(item => {
        let obj: any =
          item[item.materialType === "MINIPROGRAM" ? "miniProgram" : "image"];
        obj.materialType = item.materialType;
        return obj;
      });
      for (let k in form) {
        form[k] = resData[k];
      }

      await getTagList();
      form.tag = tagShow(resData.tag || [], tagListInit.value);

      resData.ruleTemplateId &&
        ruleReferConfiglRef.value.ruleChange(form.ruleTemplateId);
      // 更新UI配置预览
      form.config && UIConfigRef.value.changeConfig(form.config);
      form.repeatedlyTopicConfig &&
        UIConfigRef.value.changeRepeatedlyConfig(form.repeatedlyTopicConfig);
      welcomeMaterialRef.value.getPicUrl(form.materials);
      if (!form.miniProgramId) {
        form.miniProgramId = miniDefault;
      }
    } else {
      let initData = getInitData();
      delete form.id;
      for (let key in initData) {
        form[key] = initData[key];
      }
    }
    changeWelcomeMsg();
  } finally {
    loading.value = false;
  }
});
</script>
<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <el-form
        :model="form"
        :class="device"
        :label-width="device !== 'mobile' ? '140px' : ''"
        ref="formRef"
        :rules="rules"
      >
        <el-form-item label="场景名称：" prop="name">
          <el-input
            class="d-name"
            v-model.trim="form.name"
            clearable
            :disabled="type === 'detail'"
          />
        </el-form-item>
        <el-form-item label="活码场景类型：" prop="channelGroup">
          <el-select
            v-model="form.channelGroup"
            clearable
            :disabled="type === 'detail'"
          >
            <el-option
              v-for="item in typeList"
              :label="item.name"
              :value="item.id"
              :key="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="选择企微账号：" prop="accountId">
          <el-select
            v-model="form.accountId"
            placeholder="请选择企微账号"
            :disabled="!!id"
            @change="changeWechat"
          >
            <el-option
              v-for="item in wechatList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <RuleReferConfig
          ref="ruleReferConfiglRef"
          v-model:ruleTemplateId="form.ruleTemplateId"
          v-model:loading="loading"
          :type="type"
        />
        <TagConfigs
          :type="type"
          :tagListInit="tagListInit"
          v-model:tag="form.tag"
        />
        <!-- <el-form-item label="线索时长：" prop="clueInvalidDay">
          <el-select
            v-model="form.clueInvalidDay"
            :disabled="type === 'detail'"
          >
            <el-option
              v-for="item in timeLongList"
              :label="item.name"
              :value="item.id"
              :key="item.id"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="添加备注：" prop="remark">
          <el-input
            v-model.trim="form.remark"
            clearable
            :disabled="type === 'detail'"
            style="width: 300px"
          >
            <template #append>- 客户昵称</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="设置欢迎语："
          prop="welcome"
          style="max-width: 500px"
        >
          <el-input
            clearable
            :disabled="type === 'detail'"
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 10 }"
            maxlength="1000"
            show-word-limit
            v-model="form.welcome"
          />
          <IconifyIconOffline
            class="d-emoji-close"
            icon="circle-close"
            @click="form.welcome = ''"
            v-if="type !== 'detail'"
            v-show="!!form.welcome"
          />
        </el-form-item>
        <WelcomeMaterial
          ref="welcomeMaterialRef"
          v-model:materials="form.materials"
          :type="type"
          :welcome="form.welcome"
        />
        <el-form-item label="分时段欢迎语">
          <el-switch
            v-model="form.welcomeMsgSwitch"
            :active-value="2"
            :inactive-value="1"
            :disabled="type === 'detail'"
            @change="changeWelcomeMsgSwitch"
          />
        </el-form-item>
        <template v-if="form.welcomeMsgSwitch === 2">
          <el-form-item label="时段欢迎语" prop="welcomeMsgId">
            <el-select
              v-model="form.welcomeMsgId"
              placeholder="请选择时段欢迎语"
              clearable
              @change="changeWelcomeMsg"
              :disabled="type === 'detail'"
            >
              <el-option
                v-for="(item, index) in welcomeMsgList"
                :key="index"
                :label="item.welcomeName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <template v-if="form.welcomeMsgId">
            <div
              v-for="(item, index) in currentWelcomMsg?.welcomeData"
              :key="index"
            >
              <div class="time-form">
                <div>时间段{{ index + 1 }}：</div>
                <div class="time-form-item">
                  <el-form-item label="发送时间：">
                    <div>
                      <div v-for="day in item.sendTime" :key="day.week">
                        <div style="display: block">
                          周{{ capitalizationList[day.week - 1] }}
                          {{
                            dayjs(
                              (dayjs().startOf("d").unix() + day.startAt) *
                                1000 +
                                28800000
                            ).format("HH:mm:ss")
                          }}
                          -
                          {{
                            dayjs(
                              (dayjs().startOf("d").unix() + day.endAt) * 1000 +
                                28800000
                            ).format("HH:mm:ss")
                          }}
                        </div>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="欢迎语内容：" style="max-width: 500px">
                    <el-input
                      v-model="item.welcomeMsg"
                      type="textarea"
                      :autosize="{ minRows: 6, maxRows: 10 }"
                      disabled
                    />
                  </el-form-item>
                  <WelcomeMaterial
                    ref="welcomeMaterialRefs"
                    v-model:materials="item.materials"
                    type="detail"
                    :welcome="item.welcomeMsg"
                  />
                </div>
              </div>
            </div>
          </template>
        </template>
        <UIConfig
          ref="UIConfigRef"
          v-model:loading="loading"
          v-model:config="form.config"
          v-model:repeatedlyTopicConfig="form.repeatedlyTopicConfig"
          :type="type"
        />
        <el-form-item label="选择活码类型：" prop="fetchCustomerType">
          <el-select
            v-model="form.fetchCustomerType"
            :disabled="type === 'detail'"
          >
            <el-option label="小程序" :value="1" />
            <el-option label="获客助手" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.fetchCustomerType === 1"
          label="选择小程序"
          prop="miniProgramId"
        >
          <el-select v-model="form.miniProgramId" :disabled="type === 'detail'">
            <el-option
              v-for="item in miniList"
              :key="item.id"
              :value="item.id"
              :label="item.isUsed ? item.name + ' （默认）' : item.name"
            />
          </el-select>
        </el-form-item>
        <AutoSendCourse
          ref="AutoSendCourseRef"
          v-model:form="form"
          :type="type"
        />
        <el-form-item>
          <el-button @click="closePage(id, 'channel')">返回</el-button>
          <el-button
            type="primary"
            @click="submitForm(formRef)"
            v-if="type !== 'detail'"
          >
            确定
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<style scoped lang="scss">
.d-name {
  width: 250px !important;
}
.d-emoji-close {
  position: absolute;
  right: -40px;
  bottom: 0;
  cursor: pointer;
  opacity: 0.5;
  font-size: 28px;
}
.mobile .d-emoji-close {
  right: 0px;
  top: -30px;
}
.time-form {
  padding: 10px 40px;
  padding-left: 80px;
  background-color: #f5f5f3;
  margin-bottom: 10px;
  &-item {
    margin: 10px;
  }
  .check-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
}
</style>
