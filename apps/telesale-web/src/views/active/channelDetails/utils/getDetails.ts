/*
 * @Date         : 2024-11-27 16:34:00
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { detailChannel } from "/@/api/active";

function getDetails(params) {
  return detailChannel(params)
    .then(({ data }: { data: any }) => {
      data.info.configuration = data.configuration;
      data.info.materials = data.materials;
      data.info.miniProgramId = data.miniProgram?.id || "";
      data.info.welcomeMsgSwitch = data.welcomeMsgSwitch || 1;
      data.info.welcomeMsgId = data.welcomeMsgId || undefined;
      data.info.repeatedlyTopicInfo = data.repeatedlyTopicInfo;
      return data.info;
    })
    .catch(() => {
      return {};
    });
}

export default getDetails;
