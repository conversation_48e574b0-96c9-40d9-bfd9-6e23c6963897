<script setup lang="ts">
import { ref, reactive, computed } from "vue";
import UITemplate from "../../uiConfigDetails/components/UITemplate.vue";
import { getPic } from "/@/api/order";

interface Props {
  config: any;
  repeatedlyTopicConfig: any;
  loading: boolean;
  type: string;
}
const props = defineProps<Props>();

interface Emits {
  (e: "update:config", val: any): void;
  (e: "update:repeatedlyTopicConfig", val: any): void;
  (e: "update:loading", val: boolean): void;
}

const emit = defineEmits<Emits>();

const config = computed({
  get() {
    return props.config;
  },
  set(val: any) {
    emit("update:config", val);
  }
});

const repeatedlyTopicConfig = computed({
  get() {
    return props.repeatedlyTopicConfig;
  },
  set(val: any) {
    emit("update:repeatedlyTopicConfig", val);
  }
});

const loading = computed({
  get() {
    return props.loading;
  },
  set(val: boolean) {
    emit("update:loading", val);
  }
});
const picUrl = reactive({
  background: "",
  button: ""
});

const repeatedlyPicUrl = reactive({
  background: "",
  button: ""
});

//获取图片
function getPicMath(val, key, configType = "config") {
  const targetPicUrl = configType === "config" ? picUrl : repeatedlyPicUrl;
  key === "background" && (loading.value = true);
  getPic(val)
    .then(({ data }: { data: any }) => {
      targetPicUrl[key] = data;
      key === "background" && (loading.value = false);
    })
    .catch(() => {
      key === "background" && (loading.value = false);
    });
}

function changeConfig(val) {
  if (!val) return;
  getPicMath(val.background, "background", "config");
  if (val.button) {
    getPicMath(val.button, "button", "config");
  } else {
    picUrl.button = "";
  }
}

function changeRepeatedlyConfig(val) {
  if (!val) return;
  getPicMath(val.background, "background", "repeatedly");
  if (val.button) {
    getPicMath(val.button, "button", "repeatedly");
  } else {
    repeatedlyPicUrl.button = "";
  }
}

let configList = ref([]);

defineExpose({
  configList,
  changeConfig,
  changeRepeatedlyConfig
});
</script>

<template>
  <el-form-item label="首次活码主题：" prop="config">
    <el-select
      v-model="config"
      filterable
      clearable
      value-key="id"
      :disabled="type === 'detail'"
      @change="changeConfig"
    >
      <el-option
        v-for="item in configList"
        :label="item.name"
        :value="item"
        :key="item.id"
      />
    </el-select>
  </el-form-item>
  <el-form-item
    label="首次配置页展示："
    style="max-height: 360px"
    v-if="config"
  >
    <UITemplate
      ref="configUITemplateRef"
      :picUrl="picUrl"
      :bottomColor="config?.bottomColor"
      :navigationFontColor="config?.navigationFontColor"
      type="small"
    />
  </el-form-item>
  <el-form-item label="多次活码主题：">
    <el-select
      v-model="repeatedlyTopicConfig"
      filterable
      clearable
      value-key="id"
      :disabled="type === 'detail'"
      @change="changeRepeatedlyConfig"
    >
      <el-option
        v-for="item in configList"
        :label="item.name"
        :value="item"
        :key="item.id"
      />
    </el-select>
  </el-form-item>
  <el-form-item
    label="多次配置页展示："
    style="max-height: 360px"
    v-if="repeatedlyTopicConfig"
  >
    <UITemplate
      ref="repeatedlyUITemplateRef"
      :picUrl="repeatedlyPicUrl"
      :bottomColor="repeatedlyTopicConfig?.bottomColor"
      :navigationFontColor="repeatedlyTopicConfig?.navigationFontColor"
      type="small"
    />
  </el-form-item>
</template>
