# Telesale Web 电销管理平台

> 智能化电销管理平台，提供AI质检、任务管理、数据分析等核心业务功能

## 📖 业务概述

Telesale Web 是一个专为电销行业打造的智能化管理平台，通过AI技术赋能电销业务，提升质检效率和管理水平。平台涵盖从任务分配到质检分析的完整业务流程，为电销团队提供全方位的数字化解决方案。

### 🎯 核心业务功能

#### AI质检系统
- **智能质检** - 基于AI算法的通话质量自动评估
- **练习模式** - 为坐席提供模拟练习环境，提升业务技能
- **违禁词检测** - 实时监控通话内容，确保合规性
- **质检报告** - 生成详细的质检分析报告

#### 任务管理系统
- **任务分配** - 智能化任务分配和调度
- **进度跟踪** - 实时监控任务执行状态
- **成员管理** - 团队成员信息和权限管理
- **绩效评估** - 基于数据的绩效考核体系

#### 数据分析中心
- **通话分析** - 通话记录的深度数据挖掘
- **质检统计** - 质检结果的多维度统计分析
- **趋势预测** - 基于历史数据的业务趋势分析
- **可视化报表** - 直观的数据可视化展示

#### 系统管理
- **用户权限** - 细粒度的权限控制体系
- **组织架构** - 灵活的组织结构管理
- **系统配置** - 业务规则和参数配置
- **模板管理** - 质检模板和话术模板管理

## 📋 功能迭代记录

### 2025-06-30 对练机器人优化

- [CountdownRing 倒计时圆环组件](./src/views/aiQualityInspection/myTask/exercise/components/CountdownRing.md) - 新增发言倒计时圆环组件，支持动态颜色变化和超时提示
- [AI质检练习组件](./src/views/aiQualityInspection/myTask/exercise/README.md) - 完善练习模块文档，包含完整的业务流程和技术实现说明
- 课程管理优化 - 新增对话间隔时长配置，支持1-999秒的发言倒计时设置
- 语音处理优化 - 延长WebSocket等待时间至3秒，确保语音转文字完整性
- 聊天功能增强 - 优化TTS状态管理，新增消息超时状态字段
- 评估页面优化 - 改进轮询机制，优化任务得分获取提示信息
- 网络请求优化 - 完善错误处理逻辑，提供更具体的错误信息
- 人工复检优化 - 优化复检按钮交互，仅支持复检一次

### 2025-01-27 功能更新

- [违禁词检测功能](./src/views/aiQualityInspection/myTask/exercise/hooks/违禁词检测功能.md) - AI质检练习模块新增违禁词检测功能，支持实时检测和调试工具
- [音频播放功能增强](./src/views/AISupport/Quality/DetailDrawer/children/音频播放功能增强.md) - Audio组件新增直接下载功能，支持跨域音频下载
- [NexusTable组件增强](./src/components/Nexus/NexusTable/NexusTable组件增强.md) - 新增total属性支持，允许自定义分页总数数据来源
- 练习流程优化 - 修复练习抽屉关闭时计时器资源清理问题
- 任务管理优化 - 优化任务成员详情弹窗和课程详情逻辑
- 音频播放器组件 - 在任务评估页面添加音频播放器组件

## 📁 项目结构

```text
telesale-web/
├── src/
│   ├── api/                    # API接口定义
│   ├── assets/                 # 静态资源
│   ├── businessComponents/     # 业务组件
│   ├── components/            # 通用组件
│   │   ├── Nexus/            # Nexus组件库
│   │   └── common/           # 公共组件
│   ├── directives/           # 自定义指令
│   ├── hooks/                # 组合式函数
│   ├── layout/               # 布局组件
│   ├── router/               # 路由配置
│   ├── store/                # 状态管理
│   ├── style/                # 全局样式
│   ├── types/                # 类型定义
│   ├── utils/                # 工具函数
│   ├── views/                # 页面组件
│   │   ├── aiQualityInspection/  # AI质检模块
│   │   ├── AISupport/            # AI支持模块
│   │   └── ...                   # 其他业务模块
│   ├── App.vue               # 根组件
│   └── main.ts               # 入口文件
├── build/                    # 构建配置
├── types/                    # 全局类型定义
├── Docs/                     # 项目文档
├── package.json              # 项目配置
├── vite.config.ts           # Vite配置
├── tsconfig.json            # TypeScript配置
├── uno.config.ts            # UnoCSS配置
└── README.md                # 项目文档
```

## 📚 文档索引

### 🧩 组件文档

#### AI质检模块

- [AI质检我的任务模块](./src/views/aiQualityInspection/myTask/README.md) - 完整的AI质检任务管理系统
- [AI质检练习组件](./src/views/aiQualityInspection/myTask/exercise/README.md) - AI质检练习核心组件

#### 通用组件

- [音频播放功能增强](./src/views/AISupport/Quality/DetailDrawer/children/音频播放功能增强.md) - Audio组件的直接下载功能增强
- [NexusTable组件增强](./src/components/Nexus/NexusTable/NexusTable组件增强.md) - NexusTable组件的total属性支持功能

### 📖 业务文档

#### Docs目录文档

- [需求文档](./Docs/Demand/) - 业务需求和功能规格说明
- [技术文档](./Docs/Tech/) - 技术实现和架构设计文档

### 🛠️ 开发文档

- [需求完成生成文档工作流规范](../../工作流/需求完成文档生成工作流.md) - 需求完成后的文档生成标准流程
- [业务组件文档生成规则](../../工作流/业务组件文档生成工作流.md) - 复杂业务组件的文档编写规范
- [时间获取规则](../../方法/时间获取方法.md) - 项目中时间获取的标准规范和验证方法

## 🧩 全局组件

### Icon组件

项目在 `index.html` 中引入了 iconfont 图标库，可以通过 `common-icon` 组件使用：

```html
<common-icon :name="icon-name" :style="customStyle" />
```

### Nexus组件库

项目内置了 Nexus 组件库，提供了丰富的业务组件：

- **NexusTable** - 增强型表格组件
- **NexusForm** - 表单组件
- **NexusDatePicker** - 日期选择器
- 更多组件请查看 `src/components/Nexus/` 目录

## 🏗️ 业务架构

### 业务流程

```text
┌─────────────────────────────────────────────────────────────┐
│                        用户管理层                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   权限控制      │  │   角色管理      │  │   组织架构    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        业务处理层                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   任务管理      │  │   AI质检        │  │   数据分析    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                        数据存储层                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   通话记录      │  │   质检结果      │  │   统计数据    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 模块关系

- **AI质检模块**: 核心业务模块，提供智能质检、练习系统、违禁词检测等功能
- **任务管理模块**: 支撑业务运营，包括任务分配、进度跟踪、成员管理
- **数据分析模块**: 业务洞察工具，提供报表生成、统计分析、可视化展示
- **系统管理模块**: 基础支撑模块，包含用户权限、配置管理、日志监控

## 📞 业务支持

如有业务相关问题，请通过以下方式联系：

- 业务文档: 查看项目 `Docs/` 目录
- 功能说明: 参考各模块的 README.md 文档
- 技术支持: 参考根目录的开发规范文档

## 📄 许可证

本项目采用 MIT 许可证，详情请查看 [LICENSE](./LICENSE) 文件。