# API接口变更文档

## 📋 基本信息

**分支名称**: feature/excellent-recording-api-update
**更新时间**: 2025-07-10
**API规范版本**: telesale-robot-api v1.0.0

## 🔄 本次变更概述

### 涉及接口

| 接口名称 | 接口路径 | 变更类型 | 变更说明 |
|---------|---------|---------|---------|
| 获取优秀案例列表 | /admin/excellentCase | 修改 | 新增showPage和audioTags查询参数 |
| 设置案例展示页面 | /admin/excellentCase/setShowPage | 新增 | 新增接口用于设置案例的展示页面 |

### 字段变更详情

#### /admin/excellentCase (GET)

**变更类型**: 新增字段

| 字段名 | 变更前类型 | 变更后类型 | 必填性 | 变更说明 |
|--------|-----------|-----------|--------|---------|
| showPage | - | integer(int64) | 否 | 展示页面,1:首通优秀录音库，2.后续跟进录音库 |
| audioTags | - | array[string] | 否 | 录音标签数组，用于筛选特定标签的录音 |

#### /admin/excellentCase/setShowPage (POST)

**变更类型**: 新增接口

**请求参数**:
```typescript
interface SetShowPageRequest {
  actionId: string;  // 通话ID
  showPage: number;  // 展示页面,1:首通优秀录音库，2.后续跟进录音库
}
```

**响应数据**:
```typescript
interface SetShowPageReply {
  // 空对象，仅返回状态码
}
```

#### excellentcaseCaseAppraiseInstance Schema

**变更类型**: 新增字段

| 字段名 | 变更前类型 | 变更后类型 | 必填性 | 变更说明 |
|--------|-----------|-----------|--------|---------|
| audioTags | - | array[string] | 否 | 录音标签数组 |
| addToFollowUpAudioLib | - | boolean | 否 | 是否添加到后续跟进录音库 |

## 🎯 前端适配说明

### 代码变更点

1. **接口定义更新**
   - 文件路径: `src/api/AIQualityInspection/excellentCases.ts`
   - 变更内容: 
     - 更新 `ExcellentCasesQueryParams` 接口，新增 `showPage` 和 `audioTags` 字段
     - 更新 `CaseAppraiseInstance` 接口，新增 `audioTags` 和 `addToFollowUpAudioLib` 字段
     - 新增 `SetShowPageRequest` 接口和 `setExcellentCaseShowPage` 函数

2. **组件适配**
   - 文件路径: `src/views/daily/excellentRecording/components/FollowUpRecording.vue`
   - 变更内容:
     - 替换假数据生成函数为真实API调用
     - 使用 `getExcellentCasesList` API，设置 `showPage: 2` 筛选后续跟进录音
     - 添加录音标签筛选功能，通过 `audioTags` 参数实现

3. **人工复核弹窗适配**
   - 文件路径: `src/views/aiQualityInspection/excellentCases/components/AIReviewDialog.vue`
   - 变更内容:
     - 在选择添加至后续跟进录音库时调用 `setExcellentCaseShowPage` API
     - 设置 `showPage: 2` 将案例标记为后续跟进录音库

4. **添加案例弹窗适配**
   - 文件路径: `src/views/aiQualityInspection/excellentCases/components/AddCaseDialog.vue`
   - 变更内容:
     - 在添加案例时调用 `setExcellentCaseShowPage` API
     - 根据案例类型设置对应的 `showPage` 值（1:首通优秀录音库，2:后续跟进录音库）

5. **首通优秀录音组件适配**
   - 文件路径: `src/views/daily/excellentRecording/components/FirstExcellentRecording.vue`
   - 变更内容:
     - 添加 `showPage: 1` 筛选条件，确保只显示首通优秀录音库的数据

### 兼容性处理

```typescript
// 1. 后续跟进录音列表API调用参数构建
const apiParams: ExcellentCasesQueryParams = {
  // 筛选条件：展示页面为后续跟进录音库
  showPage: 2, // 2表示后续跟进录音库
  // AI评级为A级（后续跟进录音的基础条件）
  aiAppraiseScore: "A"
};

// 录音标签筛选
if (params.recordingTag) {
  apiParams.audioTags = [params.recordingTag];
}

// 处理返回数据，确保包含录音标签信息
const processedList = response.list?.map(item => ({
  ...item,
  // 从AI评级或人工评级中提取录音标签
  recordingTag: item.aiAppraise?.audioTags?.[0] || item.humanAppraise?.audioTags?.[0] || null
})) || [];

// 2. 设置案例展示页面API调用
// 在人工复核时添加至后续跟进录音库
if (formData.addToFollowUp === "是") {
  await setExcellentCaseShowPage({
    actionId: props.rowData.actionId,
    showPage: 2 // 2表示后续跟进录音库
  });
}

// 在添加案例时设置展示页面
const showPageParams = {
  actionId: props.rowData.actionId,
  showPage: formData.caseType === "后续跟进录音" ? 2 : 1
};
await setExcellentCaseShowPage(showPageParams);
```

## 📚 相关文档

- [需求文档](./index.md) - 原始需求文档索引
- [后续跟进录音需求](./后续跟进录音.md) - 详细需求说明
- [实现总结](./实现总结.md) - 功能实现总结

## 📋 历史变更记录

### v1.0.0 (2025-07-10)

- **新增**: showPage字段 - 用于区分首通优秀录音库和后续跟进录音库
- **新增**: audioTags字段 - 支持录音标签筛选功能
- **新增**: /admin/excellentCase/setShowPage接口 - 设置案例展示页面
- **新增**: CaseAppraiseInstance中的audioTags和addToFollowUpAudioLib字段
- **更新**: ExcellentCaseItem接口新增showPage和audioTags字段
- **更新**: ExcellentCaseUpdateParams接口新增humanAppraise、followUpRecording、recordingTag字段
- **影响**: 后续跟进录音组件从假数据切换为真实API调用，支持标签筛选功能

## 🔧 技术实现细节

### API调用流程

1. **后续跟进录音列表获取**:
   ```
   用户访问后续跟进录音Tab → 
   调用getExcellentCasesList API → 
   设置showPage=2筛选条件 → 
   返回后续跟进录音数据
   ```

2. **录音标签筛选**:
   ```
   用户选择录音标签 → 
   构建audioTags参数 → 
   重新调用API获取筛选结果
   ```

3. **数据处理**:
   ```
   API返回数据 → 
   提取audioTags字段作为recordingTag → 
   渲染到表格组件
   ```

### 错误处理

- API调用失败时显示错误提示
- 数据为空时返回空列表
- 网络异常时提供重试机制

## ⚠️ 注意事项

1. **数据一致性**: 确保showPage字段正确设置，避免数据混乱
2. **标签映射**: 录音标签从API返回的audioTags数组中提取第一个元素
3. **向后兼容**: 保持与现有组件接口的兼容性
4. **性能考虑**: 大量数据时考虑分页加载和虚拟滚动
