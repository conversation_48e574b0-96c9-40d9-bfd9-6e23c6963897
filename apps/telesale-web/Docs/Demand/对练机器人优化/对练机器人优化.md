# 对练机器人优化

## 文档内容总结

本文档描述了对练机器人功能的优化需求，主要针对一线主管测试反馈后的改进建议。

**核心目标：** 优化对练机器人的用户体验，提升销售人员使用效率和交互体验

**主要变更内容：**
- **响应时限优化：** 新增AI对练台销售响应及时信息条，设置发言时限倒计时
- **语音转译优化：** 语音转译发送需等待文本转译完成后才进行发送
- **交互提示优化：** 补充AI对练台交互页面文字信息和等待提示文案
- **界面布局优化：** 调整AI评级界面大小占比和评估报告板块UI

**技术要点：** 涉及课程管理配置、对练台交互逻辑、评分详情展示等多个模块的功能优化

---

## 一、修订记录

| 修改时间 | 修改原因 | 修改后 | 通知人 |
|---------|---------|--------|--------|
|         |         |        |        |

## 二、需求概述

### 现状

基于产品给到一线主管测试后，后续大范围投入使用时，希望对以下内容进行优化：

- 补充AI对练台销售响应及时信息条（设置时限）
- 语音转译发送时需要等待文本转译内容转译完成后才进行发送
- 补充AI对练台交互页面文字信息，结束后等待AI评级出来时的提示文案
- AI评级界面大小占比，后面要改一下

## 三、术语表

| 名称 | 术语场景 | 术语例 |
|------|---------|--------|
|      |         |        |

## 四、调整范围

| 功能名称 | 类型 | 补充说明 |
|---------|------|----------|
| 课程设置 | 优化 |          |
| 对练台   | 优化 |          |
| 评分详情 | 优化 |          |

## 五、功能需求描述

### 课程管理-新增最短对话发言间隔

**新增对话间隔时长配置项：**

**功能说明：** 配置该功能后，进行对练时，需要给销售侧进行回复倒计时引导，避免销售侧等待回复时长过久而降低了销售及时回复的能力

**功能类型：** 输入框

**限制说明：**
- 支持输入范围：1-999
- 时间单位：秒
- 不允许为空，默认值30

**对练台交互展示：**
- 语音模式：当机器人语音播放完毕后，发言按钮进入倒计时状态
- 文本模式：当机器人文本生成完毕后，发言按钮进入倒计时状态

**语音按钮交互说明：**

语音按钮进入倒计时状态时，ICON图标外环增加倒计时圆环，并且根据剩余时间占比展示不同颜色交互：

- 点击语音开始发言后，暂停剩余发言倒计时时长
- 100%：圆环绿色
- 75%：圆环蓝色
- 50%：圆环黄色
- 25%：圆环红色
- 0%：麦克风下方toast提示：当前发言已经超时，请尽快回复～

**交互逻辑：**
- 发言过程中，取消发送后，倒计时按照上次倒计时进度继续倒计时
- 完成发言后，完成发送，等待后续AI结果返回后重新开始发言倒计时

### 课程管理-发言优化

**发言完成后，点击发送按钮时，需要等待语音文本全部转译完成后才正式进行发送**

- 如果点击发送时，语音未转译完成，等待转译完成后进行发送

### 课程管理-对练明细页

**评分总结生成过程中提示优化：**

当前任务结果生成中，预计耗时1-2分钟，可关闭页面，稍后查看任务结果～

**结果更新：**
- 如果在该页面进行等待，当AI结果生成完毕后，实时更新任务结果进行展示
- 语音合成完毕后，实时更新语音播放器组件

**评估报告板块UI调整：**

**对话评估报告默认：**
- 高度增加，覆盖首屏
- 整体宽度增加4/1

**任务信息板块：**
- 默认折叠展示

**课程目标删减背景描述、对话要求板块：**
- 默认折叠展示

### 优秀案例库

**人工复检按钮：**
- 变更交互，仅支持复检一次，复检完成后按钮消失

## 六、权限说明

## 七、移动端兼容说明

## 八、相关文档

## 九、测试用例

## 十、需求复盘

### 数据详情

### 好的地方

### 不足的地方
