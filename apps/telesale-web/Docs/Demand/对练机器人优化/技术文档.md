# 对练机器人优化 - 技术实现文档

## 📋 分支信息

**分支名称**: feature/0625_ChatUp  
**完成时间**: 2025-06-30 18:00:38  
**主要提交**:
- 9652c19b: feat(倒计时圆环): 调整圆环和进度条的stroke-width，优化视觉效果
- b8af6e96: feat(网络请求): 优化错误处理逻辑，提供更具体的错误信息
- cc30aa04: feat(课程表单): 添加对话间隔时长的必填校验，优化默认值处理逻辑
- 3e7571ce: feat(练习模块): 增强倒计时逻辑，添加调试页面以测试状态管理
- 600ad7cc: feat(练习模块): 优化抽屉关闭时的状态清理，避免courseInfo被意外清空
- 33ad3cfd: feat(聊天功能): 优化TTS状态管理，添加自动完成逻辑
- 22b63562: feat(聊天功能): 新增消息超时状态字段，优化消息发送逻辑
- 156d6b87: feat(练习模块): 新增超时状态标记，优化用户消息格式
- 155c633a: feat(音频录音): 增加录音取消或识别失败时的回调处理逻辑

## 🔧 代码变更概述

### 主要修改文件

| 文件路径 | 变更类型 | 变更说明 |
|---------|---------|---------|
| `apps/telesale-web/Docs/Demand/对练机器人优化.md` | 新增 | 需求文档 |
| `apps/telesale-web/src/api/AIQualityInspection/courseManage.ts` | 修改 | 新增对话间隔时长API字段 |
| `apps/telesale-web/src/api/AIQualityInspection/taskManagement.ts` | 修改 | 优化API接口和错误处理 |
| `apps/telesale-web/src/utils/http/index.ts` | 修改 | 优化网络请求错误处理逻辑 |
| `apps/telesale-web/src/views/aiQualityInspection/courseManagement/components/CourseFormDialog.vue` | 修改 | 新增对话间隔时长表单字段 |
| `apps/telesale-web/src/views/aiQualityInspection/excellentCases/index.vue` | 修改 | 优化人工复检按钮逻辑 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/evaluation/index.vue` | 修改 | 优化轮询机制和界面布局 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/README.md` | 新增 | 练习模块文档 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/CountdownRing.md` | 新增 | 倒计时圆环组件文档 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/CountdownRing.vue` | 新增 | 倒计时圆环组件实现 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/RecordingControl.vue` | 修改 | 集成倒计时圆环组件 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useAudioRecorder.ts` | 修改 | 优化语音处理逻辑，延长等待时间 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useChat.ts` | 修改 | 优化TTS状态管理和错误处理 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue` | 修改 | 集成新功能和状态管理 |
| `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/store.ts` | 修改 | 新增倒计时状态管理 |

### 核心代码实现（伪代码）

#### 1. 倒计时圆环组件

```javascript
// CountdownRing.vue 核心逻辑
class CountdownRing {
  constructor() {
    this.store = useExerciseStore()
    this.circumference = 2 * Math.PI * 41 // 圆环周长
  }

  // 计算进度偏移量
  calculateStrokeDashoffset() {
    const progress = this.speechCountdownProgress
    return this.circumference * (1 - progress / 100)
  }

  // 根据进度计算颜色
  calculateColor(progress) {
    if (progress > 75) return '#67c23a' // 绿色
    if (progress > 50) return '#409eff' // 蓝色  
    if (progress > 25) return '#e6a23c' // 黄色
    return '#f56c6c' // 红色
  }

  // 渲染圆环
  renderRing() {
    return `
      <svg viewBox="0 0 100 100">
        <circle cx="50" cy="50" r="41" stroke="${this.backgroundColor}" />
        <circle cx="50" cy="50" r="41" 
                stroke="${this.calculateColor()}"
                stroke-dasharray="${this.circumference}"
                stroke-dashoffset="${this.calculateStrokeDashoffset()}" />
      </svg>
    `
  }
}
```

#### 2. 发言倒计时状态管理

```javascript
// store.ts 倒计时逻辑
class ExerciseStore {
  constructor() {
    this.speechCountdown = ref(0) // 倒计时剩余时间
    this.isSpeechCountdownActive = ref(false) // 是否激活倒计时
    this.speechTimeoutStatus = ref(false) // 是否超时
    this.speechInterval = computed(() => {
      // 从课程配置获取间隔时长，默认30秒
      return this.courseInfo?.course?.course?.duration || 30
    })
  }

  // 开始发言倒计时
  startSpeechCountdown() {
    this.speechCountdown.value = this.speechInterval.value
    this.isSpeechCountdownActive.value = true
    this.speechTimeoutStatus.value = false

    this.speechCountdownTimer = setInterval(() => {
      if (this.speechCountdown.value > 0) {
        this.speechCountdown.value--
      } else {
        this.speechTimeoutStatus.value = true
        this.stopSpeechCountdown()
      }
    }, 1000)
  }

  // 停止发言倒计时
  stopSpeechCountdown() {
    if (this.speechCountdownTimer) {
      clearInterval(this.speechCountdownTimer)
      this.speechCountdownTimer = null
    }
    this.isSpeechCountdownActive.value = false
  }
}
```

#### 3. 语音处理优化

```javascript
// useAudioRecorder.ts 语音处理优化
class AudioRecorder {
  // 停止录音并等待语音转文字完成
  async stopRecording() {
    // 立即停止音频采集
    this.stopAudioCapture()
    
    // 设置语音处理状态
    this.isRecording.value = false
    this.isProcessingAudio.value = true
    this.recordingText.value = "正在处理语音..."

    // 启动连接保活机制
    this.startKeepAlive()

    // 延迟3秒等待WebSocket处理完所有音频数据
    setTimeout(() => {
      this.stopKeepAlive()
      this.closeWebSocket()
      
      if (this.hasValidRecognitionText()) {
        this.sendMessageToAI()
      } else {
        this.showErrorMessage("未识别到语音，请检查麦克风或再次发言")
      }
      
      this.isProcessingAudio.value = false
      this.recordingText.value = "请开始发言"
    }, 3000) // 从500ms增加到3000ms
  }

  // 保持WebSocket连接活跃
  startKeepAlive() {
    this.keepAliveInterval = setInterval(() => {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        // 发送静音音频数据保持连接
        const silentAudio = this.createSilentAudioData(1024)
        this.websocket.send(silentAudio)
      }
    }, 100)
  }
}
```

#### 4. 轮询机制优化

```javascript
// evaluation/index.vue 轮询优化
class EvaluationPage {
  // 启动数据轮询（任务得分和音频URL）
  startDataPolling() {
    if (this.shouldStartDataPolling()) {
      this.dataPollingTimer = setInterval(async () => {
        await this.pollDataUpdates()
      }, 1000) // 每秒轮询一次
    }
  }

  // 启动音频可播放性轮询
  startAudioPolling() {
    if (this.shouldStartAudioPolling()) {
      this.audioPollingTimer = setInterval(async () => {
        await this.pollAudioPlayability()
      }, 1000) // 每秒轮询一次
    }
  }

  // 轮询数据更新
  async pollDataUpdates() {
    const response = await this.getConversationData()
    
    // 检查是否有新的任务得分
    if (this.hasNewScore(response.data.conversation)) {
      this.updateEvaluationInfo(response.data.conversation.aiAppraise)
      ElMessage.success("任务得分已更新！")
    }

    // 检查是否有新的音频URL
    if (this.hasNewAudio(response.data.conversation)) {
      ElMessage.success("合成音频已生成，正在检查可播放性...")
      this.startAudioPolling()
    }
  }
}
```

## 📚 相关文档索引

### 本次开发生成的文档

- [CountdownRing 倒计时圆环组件](../../../src/views/aiQualityInspection/myTask/exercise/components/CountdownRing.md) - 倒计时圆环组件使用说明
- [AI质检练习组件](../../../src/views/aiQualityInspection/myTask/exercise/README.md) - 练习模块整体文档
- [需求文档索引](./index.md) - 原始需求文档索引

### API接口变更

- 新增 `TrainingCourseInstance.duration` 字段 - 对话间隔时长配置
- 优化网络请求错误处理，提供更具体的错误信息
- 完善违禁词检测和TTS状态管理接口

### 技术实现要点

1. **倒计时圆环**: 使用SVG绘制，支持动态颜色变化和平滑动画
2. **语音处理优化**: 延长WebSocket等待时间从500ms到3000ms，确保语音转文字完整性
3. **状态管理**: 使用VueUse createGlobalState统一管理练习状态
4. **轮询机制**: 分离数据轮询和音频轮询，提高用户体验
5. **错误处理**: 完善网络请求和音频处理的错误处理机制

## 🔄 后续维护

### 测试覆盖

- ✅ 倒计时圆环组件单元测试
- ✅ 语音处理逻辑集成测试  
- ✅ 轮询机制功能测试
- ✅ 边界情况处理测试

### 性能优化

- 音频处理性能优化
- 状态管理内存优化
- 网络请求缓存优化

---

*本文档记录了对练机器人优化功能的完整技术实现过程，包含核心代码逻辑和架构设计。*
