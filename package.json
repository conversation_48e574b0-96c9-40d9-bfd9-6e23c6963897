{"name": "wh_crm", "version": "1.0.0", "private": true, "scripts": {"dev:web": "turbo run dev --filter=wh_crm_v2", "dev:mobile": "turbo run dev --filter=telesale-mobile", "build:test": "turbo run build:test && npm run copy", "build:stage": "turbo run build:stage && npm run copy", "build:master": "turbo run build:master && npm run copy", "copy": "node script/copy.js", "serve": "vite preview --mode prod", "prepare": "husky install", "cz": "git add . && git cz"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "workspaces": ["apps/*", "packages/*"], "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@guanghe-pub/onion-utils": "^2.16.1", "@pureadmin/components": "^1.0.6", "@unocss/reset": "^0.53.5", "@vitejs/plugin-legacy": "^4.1.1", "@vueuse/core": "^8.4.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^0.27.2", "axios-jsonp": "^1.0.4", "axios-retry": "^3.5.1", "dayjs": "^1.11.10", "echarts": "^5.6.0", "el-table-horizontal-scroll": "^1.2.5", "element-plus": "^2.3.9", "element-resize-detector": "^1.2.3", "eruda": "^3.0.1", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash-unified": "^1.0.2", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.0.14", "pinia-plugin-persistedstate": "^3.1.0", "postcss": "^8.4.26", "qs": "^6.10.2", "resize-observer-polyfill": "^1.5.1", "responsive-storage": "^1.0.11", "typed.js": "^2.0.16", "uuid": "^11.0.3", "vant": "^4.8.0", "vconsole": "^3.15.1", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.3.4", "vue-router": "^4.0.15", "vue-types": "^4.1.1", "vue-virtual-scroller": "2.0.0-beta.8", "vxe-table": "^4.5.12", "xe-utils": "^3.5.13"}, "devDependencies": {"@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@constq/eslint-config-qzr": "^0.0.5", "@constq/qzr-utils": "^1.2.5", "@guanghe-pub/onion-oss-vite-plugin": "^0.0.3", "@iconify-icons/ep": "^1.2.4", "@iconify-icons/ri": "^1.2.1", "@iconify-json/ion": "^1.1.11", "@iconify/vue": "^3.2.0", "@pureadmin/theme": "^2.4.0", "@types/element-resize-detector": "1.1.3", "@types/jest": "^29.5.3", "@types/lodash": "^4.14.202", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.7", "@types/node": "^20.4.2", "@types/nprogress": "0.2.0", "@types/qs": "^6.9.7", "@types/rollup-plugin-visualizer": "^4.2.1", "@types/vue": "^2.0.0", "@typescript-eslint/eslint-plugin": "^6.1.0", "@typescript-eslint/parser": "^6.1.0", "@unoCSS/preset-icons": "npm:@unocss/preset-icons@^0.53.6", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.3.4", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "@vue/test-utils": "^2.4.0", "autoprefixer": "^10.4.5", "chalk": "4.1.2", "commitizen": "^4.3.0", "consola": "^3.2.3", "cross-env": "7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "^8.45.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.2.3", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.15.1", "husky": "^8.0.3", "jest": "^29.6.1", "lint-staged": "^13.2.3", "picocolors": "^1.0.0", "postcss": "^8.4.6", "postcss-html": "^1.3.0", "postcss-import": "14.0.0", "postcss-px-to-viewport-8-plugin": "^1.2.2", "postcss-scss": "^4.0.3", "prettier": "^2.5.1", "pretty-quick": "3.1.1", "rimraf": "3.0.2", "rollup": "^2.70.1", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.63.6", "stylelint": "^14.3.0", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^6.0.0", "stylelint-config-standard": "^24.0.0", "stylelint-order": "^5.0.0", "stylus": "^0.59.0", "ts-jest": "^29.1.1", "turbo": "^2.0.9", "typescript": "^5.1.6", "unocss": "^0.53.5", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^3.4.0", "vue-eslint-parser": "^8.2.0", "vue-global-api": "^0.4.1", "vue-jest": "^3.0.7", "vue-tsc": "^1.8.5"}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}, "packageManager": "pnpm@8.15.4"}