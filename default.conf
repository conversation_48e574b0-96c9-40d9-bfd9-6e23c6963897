server {
    listen   80; ## listen for ipv4; this line is default and implied
    listen   [::]:80 default ipv6only=on; ## listen for ipv6

    root /var/www/html;
    index index.html;

    server_tokens  off; # disable the Server nginx header

    server_name _; # all hostnames

    # enable gzip
    gzip on;
    gzip_disable "msie6";

    gzip_comp_level 6;
    gzip_min_length 1100;
    gzip_buffers 16 8k;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/x-javascript
        application/json
        application/xml
        application/rss+xml
        image/svg+xml;

    location ~ ^/WH_CRM_v2/([^/]+)/assets/ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        add_header Cache-Control 'no-cache,private,max-age=86400';

        set $project $1;
        rewrite ^/WH_CRM_v2/$project/(.*)$ /$1 break;

    }

    location ~ ^/WH_CRM_v2/telesale-mobile/ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        add_header Cache-Control 'no-cache,private,max-age=86400';

        try_files $uri $uri/ /WH_CRM_v2/telesale-mobile/index.html;
    }

    location ~ ^/WH_CRM_v2/telesale-phone-v2/ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        add_header Cache-Control 'no-cache,private,max-age=86400';

        try_files $uri $uri/ /WH_CRM_v2/telesale-phone-v2/index.html;
    }

    location ~ ^/WH_CRM_v2/telesale-phone/ {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        add_header Cache-Control 'no-cache,private,max-age=86400';

        try_files $uri $uri/ /WH_CRM_v2/telesale-phone/index.html;
    }

    location ~ ^/WH_CRM_v2 {
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

        add_header Cache-Control 'no-cache,private,max-age=86400';

        try_files $uri $uri/ /WH_CRM_v2/telesale-web/index.html;
    }


}
