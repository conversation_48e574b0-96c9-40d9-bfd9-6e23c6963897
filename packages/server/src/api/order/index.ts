import getHttp from "../../utils/http/ceateAixos";
const http = getHttp();
export interface UserOrderReqQuery {
  /**
   * 用户id
   */
  userId?: string;
  /**
   * 支付状态
   */
  status?: string;
}

/**
 * @description 获取用户订单（包含用户的所有订单）
 * https://yapi.yc345.tv/project/2352/interface/api/126555
 * <AUTHOR>
 * @date 2025-06-26
 * @export
 * @param {UserOrderReqQuery} params
 * @returns {Promise<any>}
 */
export const getUserOrderApi = (params: UserOrderReqQuery) => {
  return http.request(
    `get`,
    `/wuhan-datapool/info/getUserOrderWithAuthEndTime`,
    {
      params
    }
  );
};
