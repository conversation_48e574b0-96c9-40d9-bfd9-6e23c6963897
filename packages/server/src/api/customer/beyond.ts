/*
 * @Date         : 2025-07-11 15:40:48
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import getHttp from "../../utils/http/ceateAixos";
const http = getHttp();
export interface UserCoolDownStatusReqQuery {
  /**
   * 用户ID
   */
  userId: string;
}

export interface UserCoolDownStatus {
  leadsCoolDown: {
    /**
     * true:冷静期生效中,false:冷静期未生效
     */
    effective: boolean;
    /**
     * 截止时间
     */
    endTime: string;
  };
  serviceCoolDown: {
    /**
     * true:冷静期生效中,false:冷静期未生效
     */
    effective: string;
    /**
     * 截止时间
     */
    endTime: string;
  };
}

/**
 * @description 用户冷静期状态
 * https://yapi.yc345.tv/project/2352/interface/api/127480
 * <AUTHOR>
 * @date 2025-07-14
 * @export
 * @param {UserCoolDownStatusReqQuery} params
 * @returns {Promise<UserCoolDownStatus>}
 */
export const getCoolingApi = (params: UserCoolDownStatusReqQuery) => {
  return http.request<UserCoolDownStatus>(
    `get`,
    `/wuhan-datapool/info/getUserCoolDownStatus`,
    {
      params
    }
  );
};

export interface ManualLeadsFilterReqQuery {
  /**
   * 用户ID
   */
  userId: string;
}

export interface ManualLeadsFilter {
  /**
   * 是否通过
   */
  pass: boolean;
  /**
   * 不通过的原因
   */
  reason: string;
}

/**
 * @description 人工录入过滤条件
 * https://yapi.yc345.tv/project/2352/interface/api/127487
 * <AUTHOR>
 * @date 2025-07-14
 * @export
 * @param {ManualLeadsFilterReqQuery} params
 * @returns {Promise<ManualLeadsFilter>}
 */
export const getJoinPoolApi = (params: ManualLeadsFilterReqQuery) => {
  return http.request<ManualLeadsFilter>(
    `get`,
    `/wuhan-datapool/leads/manualLeadsFilter`,
    {
      params
    }
  );
};
