/*
 * @Date         : 2025-07-03 15:46:54
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import getHttp from "../../utils/http/ceateAixos";
const http = getHttp();

export interface DeleteGoodsReqBodyOther {
  id?: number[];
}

/**
 * @description 后台-批量删除商品
 * https://yapi.yc345.tv/project/2071/interface/api/126969
 * <AUTHOR>
 * @date 2025-07-03
 * @export
 * @param {DeleteGoodsReqBodyOther} data
 */
export const deleteGoodsApi = (data: DeleteGoodsReqBodyOther) => {
  return http.request(`post`, `/wuhan-marketing/goods/deleteGoods`, {
    data
  });
};

export interface SwitchGoodsReqBodyOther {
  id?: number[];
  status?: number;
}

/**
 * @description 后台-商品批量上下架
 * https://yapi.yc345.tv/project/2071/interface/api/126983
 * <AUTHOR>
 * @date 2025-07-03
 * @export
 * @param {SwitchGoodsReqBodyOther} data
 */
export const setGoodsStatusApi = (data: SwitchGoodsReqBodyOther) => {
  return http.request(`post`, `/wuhan-marketing/goods/switchGoods`, {
    data
  });
};
